/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const VerifiedVoiceLanguageResponseModel: core.serialization.ObjectSchema<serializers.VerifiedVoiceLanguageResponseModel.Raw, ElevenLabs.VerifiedVoiceLanguageResponseModel>;
export declare namespace VerifiedVoiceLanguageResponseModel {
    interface Raw {
        language: string;
        model_id: string;
        accent?: string | null;
        locale?: string | null;
        preview_url?: string | null;
    }
}

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const RenderStatus: core.serialization.Schema<serializers.RenderStatus.Raw, ElevenLabs.RenderStatus>;
export declare namespace RenderStatus {
    type Raw = "complete" | "processing" | "failed";
}

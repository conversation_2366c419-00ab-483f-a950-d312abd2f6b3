/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const RagIndexStatus: core.serialization.Schema<serializers.RagIndexStatus.Raw, ElevenLabs.RagIndexStatus>;
export declare namespace RagIndexStatus {
    type Raw = "created" | "processing" | "failed" | "succeeded" | "rag_limit_exceeded" | "document_too_small";
}

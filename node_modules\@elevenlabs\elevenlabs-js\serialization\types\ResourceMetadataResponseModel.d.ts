/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { WorkspaceResourceType } from "./WorkspaceResourceType";
import { ShareOptionResponseModel } from "./ShareOptionResponseModel";
export declare const ResourceMetadataResponseModel: core.serialization.ObjectSchema<serializers.ResourceMetadataResponseModel.Raw, ElevenLabs.ResourceMetadataResponseModel>;
export declare namespace ResourceMetadataResponseModel {
    interface Raw {
        resource_id: string;
        resource_type: WorkspaceResourceType.Raw;
        creator_user_id?: string | null;
        role_to_group_ids: Record<string, string[]>;
        share_options: ShareOptionResponseModel.Raw[];
    }
}

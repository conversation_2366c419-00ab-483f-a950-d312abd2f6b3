"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[agentId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useAgentConversation.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useAgentConversation.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAgentConversation: () => (/* binding */ useAgentConversation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var voice_stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! voice-stream */ \"(app-pages-browser)/../node_modules/voice-stream/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAgentConversation auto */ \n\nconst sendMessage = (websocket, request)=>{\n    if (websocket.readyState !== WebSocket.OPEN) {\n        return;\n    }\n    websocket.send(JSON.stringify(request));\n};\nconst useAgentConversation = (agentId)=>{\n    const websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [userTranscript, setUserTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [agentResponse, setAgentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isPlayingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const audioBufferRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const bufferTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const workingSampleRateRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { startStreaming, stopStreaming } = (0,voice_stream__WEBPACK_IMPORTED_MODULE_1__.useVoiceStream)({\n        onAudioChunked: {\n            \"useAgentConversation.useVoiceStream\": (audioData)=>{\n                if (!websocketRef.current) return;\n                sendMessage(websocketRef.current, {\n                    user_audio_chunk: audioData\n                });\n            }\n        }[\"useAgentConversation.useVoiceStream\"]\n    });\n    // Initialize audio context\n    const initAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[initAudioContext]\": async ()=>{\n            if (!audioContextRef.current) {\n                try {\n                    const AudioContextClass = window.AudioContext || window.webkitAudioContext;\n                    audioContextRef.current = new AudioContextClass();\n                    // Resume audio context if it's suspended (required by some browsers)\n                    if (audioContextRef.current.state === 'suspended') {\n                        await audioContextRef.current.resume();\n                    }\n                } catch (error) {\n                    console.error('Failed to initialize audio context:', error);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[initAudioContext]\"], []);\n    // Play audio from base64 - try as MP3 first, then fallback to PCM\n    const playAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[playAudio]\": async (base64Audio)=>{\n            try {\n                console.log('Attempting to play audio chunk, length:', base64Audio.length);\n                // Method 1: Try as MP3 using HTML Audio (ElevenLabs default format)\n                try {\n                    console.log('🎵 Trying as MP3 format...');\n                    const audio = new Audio(\"data:audio/mpeg;base64,\".concat(base64Audio));\n                    audio.volume = 0.8;\n                    return new Promise({\n                        \"useAgentConversation.useCallback[playAudio]\": (resolve, reject)=>{\n                            let resolved = false;\n                            const cleanup = {\n                                \"useAgentConversation.useCallback[playAudio].cleanup\": ()=>{\n                                    audio.onended = null;\n                                    audio.onerror = null;\n                                    audio.oncanplaythrough = null;\n                                }\n                            }[\"useAgentConversation.useCallback[playAudio].cleanup\"];\n                            audio.onended = ({\n                                \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                    if (!resolved) {\n                                        resolved = true;\n                                        cleanup();\n                                        console.log('✅ Audio played successfully as MP3');\n                                        resolve();\n                                    }\n                                }\n                            })[\"useAgentConversation.useCallback[playAudio]\"];\n                            audio.onerror = ({\n                                \"useAgentConversation.useCallback[playAudio]\": (error)=>{\n                                    if (!resolved) {\n                                        cleanup();\n                                        console.log('❌ MP3 playback failed, trying other formats...');\n                                        reject(error);\n                                    }\n                                }\n                            })[\"useAgentConversation.useCallback[playAudio]\"];\n                            audio.oncanplaythrough = ({\n                                \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                    console.log('🎶 MP3 audio ready to play');\n                                    audio.play().catch(reject);\n                                }\n                            })[\"useAgentConversation.useCallback[playAudio]\"];\n                            // Fallback timeout\n                            setTimeout({\n                                \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                    if (!resolved && audio.readyState >= 2) {\n                                        audio.play().catch(reject);\n                                    }\n                                }\n                            }[\"useAgentConversation.useCallback[playAudio]\"], 500);\n                        }\n                    }[\"useAgentConversation.useCallback[playAudio]\"]);\n                } catch (mp3Error) {\n                    console.log('MP3 method failed, trying Web Audio API:', mp3Error);\n                    // Method 2: Try Web Audio API with automatic decoding\n                    if (!audioContextRef.current) {\n                        await initAudioContext();\n                    }\n                    if (!audioContextRef.current) {\n                        throw new Error('Audio context not available');\n                    }\n                    // Convert base64 to ArrayBuffer\n                    const binaryString = atob(base64Audio);\n                    const arrayBuffer = new ArrayBuffer(binaryString.length);\n                    const uint8Array = new Uint8Array(arrayBuffer);\n                    for(let i = 0; i < binaryString.length; i++){\n                        uint8Array[i] = binaryString.charCodeAt(i);\n                    }\n                    console.log('🔊 Trying Web Audio API automatic decoding...');\n                    try {\n                        // Let Web Audio API automatically decode the format\n                        const audioBuffer = await audioContextRef.current.decodeAudioData(arrayBuffer);\n                        console.log(\"✅ Auto-decoded audio: \".concat(audioBuffer.duration.toFixed(2), \"s, \").concat(audioBuffer.sampleRate, \"Hz\"));\n                        const source = audioContextRef.current.createBufferSource();\n                        source.buffer = audioBuffer;\n                        source.connect(audioContextRef.current.destination);\n                        return new Promise({\n                            \"useAgentConversation.useCallback[playAudio]\": (resolve)=>{\n                                source.onended = ({\n                                    \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                        console.log('✅ Audio played successfully via Web Audio API');\n                                        resolve();\n                                    }\n                                })[\"useAgentConversation.useCallback[playAudio]\"];\n                                source.start();\n                            }\n                        }[\"useAgentConversation.useCallback[playAudio]\"]);\n                    } catch (decodeError) {\n                        console.log('❌ Auto-decode failed:', decodeError);\n                        throw new Error('All audio playback methods failed');\n                    }\n                }\n            } catch (error) {\n                console.error('Error playing audio:', error);\n                console.log('Base64 sample:', base64Audio.substring(0, 50));\n            }\n        }\n    }[\"useAgentConversation.useCallback[playAudio]\"], [\n        initAudioContext\n    ]);\n    // Process audio queue\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingRef.current || audioQueueRef.current.length === 0) return;\n            isPlayingRef.current = true;\n            const audioData = audioQueueRef.current.shift();\n            if (audioData) {\n                try {\n                    await playAudio(audioData);\n                } catch (error) {\n                    console.error('Error in processAudioQueue:', error);\n                // Continue processing even if one audio chunk fails\n                }\n            }\n            isPlayingRef.current = false;\n            // Process next audio in queue\n            if (audioQueueRef.current.length > 0) {\n                setTimeout({\n                    \"useAgentConversation.useCallback[processAudioQueue]\": ()=>processAudioQueue()\n                }[\"useAgentConversation.useCallback[processAudioQueue]\"], 50); // Small delay between chunks\n            }\n        }\n    }[\"useAgentConversation.useCallback[processAudioQueue]\"], [\n        playAudio\n    ]);\n    // Play audio chunks directly without complex buffering\n    const handleAudioChunk = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[handleAudioChunk]\": (base64Audio)=>{\n            console.log('Adding audio chunk to queue, length:', base64Audio.length);\n            // Add to queue for sequential playback\n            audioQueueRef.current.push(base64Audio);\n            // Start processing queue if not already playing\n            if (!isPlayingRef.current) {\n                processAudioQueue();\n            }\n        }\n    }[\"useAgentConversation.useCallback[handleAudioChunk]\"], [\n        processAudioQueue\n    ]);\n    const startConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n            if (isConnected) return;\n            try {\n                // Request microphone permission\n                await navigator.mediaDevices.getUserMedia({\n                    audio: true\n                });\n                // Initialize audio context for playback\n                await initAudioContext();\n                // Get signed URL for WebSocket connection\n                const response = await fetch(\"/api/conversation/signed-url?agentId=\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to get signed URL');\n                }\n                const { signed_url } = await response.json();\n                const websocket = new WebSocket(signed_url);\n                websocket.onopen = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        sendMessage(websocket, {\n                            type: \"conversation_initiation_client_data\"\n                        });\n                        await startStreaming();\n                        setIsListening(true);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onmessage = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async (event)=>{\n                        const data = JSON.parse(event.data);\n                        // Handle ping events to keep connection alive\n                        if (data.type === \"ping\") {\n                            setTimeout({\n                                \"useAgentConversation.useCallback[startConversation]\": ()=>{\n                                    sendMessage(websocket, {\n                                        type: \"pong\",\n                                        event_id: data.ping_event.event_id\n                                    });\n                                }\n                            }[\"useAgentConversation.useCallback[startConversation]\"], data.ping_event.ping_ms || 0);\n                        }\n                        if (data.type === \"user_transcript\") {\n                            const { user_transcription_event } = data;\n                            setUserTranscript(user_transcription_event.user_transcript);\n                            console.log(\"User transcript:\", user_transcription_event.user_transcript);\n                        }\n                        if (data.type === \"agent_response\") {\n                            const { agent_response_event } = data;\n                            setAgentResponse(agent_response_event.agent_response);\n                            console.log(\"Agent response:\", agent_response_event.agent_response);\n                        }\n                        if (data.type === \"interruption\") {\n                            console.log(\"Conversation interrupted\");\n                            // Clear audio queue on interruption\n                            audioQueueRef.current = [];\n                            isPlayingRef.current = false;\n                        }\n                        if (data.type === \"audio\") {\n                            const { audio_event } = data;\n                            console.log('Received audio chunk, length:', audio_event.audio_base_64.length);\n                            // Debug: Check audio format by looking at base64 header\n                            const base64Sample = audio_event.audio_base_64.substring(0, 50);\n                            console.log('Audio base64 sample:', base64Sample);\n                            // Try to detect format from base64 header\n                            try {\n                                const binaryString = atob(audio_event.audio_base_64.substring(0, 20));\n                                const header = Array.from(binaryString).map({\n                                    \"useAgentConversation.useCallback[startConversation].header\": (c)=>c.charCodeAt(0).toString(16).padStart(2, '0')\n                                }[\"useAgentConversation.useCallback[startConversation].header\"]).join(' ');\n                                console.log('Audio header bytes:', header);\n                            } catch (e) {\n                                console.log('Could not decode header');\n                            }\n                            // Handle audio chunk directly\n                            handleAudioChunk(audio_event.audio_base_64);\n                        }\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocketRef.current = websocket;\n                websocket.onclose = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket disconnected');\n                        websocketRef.current = null;\n                        setIsConnected(false);\n                        setIsListening(false);\n                        stopStreaming();\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onerror = ({\n                    \"useAgentConversation.useCallback[startConversation]\": (error)=>{\n                        console.error('WebSocket error:', error);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                throw error;\n            }\n        }\n    }[\"useAgentConversation.useCallback[startConversation]\"], [\n        agentId,\n        isConnected,\n        startStreaming,\n        initAudioContext,\n        handleAudioChunk\n    ]);\n    const stopConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[stopConversation]\": async ()=>{\n            if (!websocketRef.current) return;\n            websocketRef.current.close();\n            setUserTranscript('');\n            setAgentResponse('');\n            audioQueueRef.current = [];\n            isPlayingRef.current = false;\n        }\n    }[\"useAgentConversation.useCallback[stopConversation]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAgentConversation.useEffect\": ()=>{\n            return ({\n                \"useAgentConversation.useEffect\": ()=>{\n                    if (websocketRef.current) {\n                        websocketRef.current.close();\n                    }\n                }\n            })[\"useAgentConversation.useEffect\"];\n        }\n    }[\"useAgentConversation.useEffect\"], []);\n    return {\n        startConversation,\n        stopConversation,\n        isConnected,\n        isListening,\n        userTranscript,\n        agentResponse\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAgentConversation.ts\n"));

/***/ })

});
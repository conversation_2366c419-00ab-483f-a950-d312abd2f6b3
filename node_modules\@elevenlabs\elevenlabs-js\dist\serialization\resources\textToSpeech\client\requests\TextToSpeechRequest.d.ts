/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as serializers from "../../../../index";
import * as ElevenLabs from "../../../../../api/index";
import * as core from "../../../../../core";
import { VoiceSettings } from "../../../../types/VoiceSettings";
import { PronunciationDictionaryVersionLocator } from "../../../../types/PronunciationDictionaryVersionLocator";
import { BodyTextToSpeechV1TextToSpeechVoiceIdPostApplyTextNormalization } from "../../types/BodyTextToSpeechV1TextToSpeechVoiceIdPostApplyTextNormalization";
export declare const TextToSpeechRequest: core.serialization.Schema<serializers.TextToSpeechRequest.Raw, Omit<ElevenLabs.TextToSpeechRequest, "enableLogging" | "optimizeStreamingLatency" | "outputFormat">>;
export declare namespace TextToSpeechRequest {
    interface Raw {
        text: string;
        model_id?: string | null;
        language_code?: string | null;
        voice_settings?: VoiceSettings.Raw | null;
        pronunciation_dictionary_locators?: PronunciationDictionaryVersionLocator.Raw[] | null;
        seed?: number | null;
        previous_text?: string | null;
        next_text?: string | null;
        previous_request_ids?: string[] | null;
        next_request_ids?: string[] | null;
        use_pvc_as_ivc?: boolean | null;
        apply_text_normalization?: BodyTextToSpeechV1TextToSpeechVoiceIdPostApplyTextNormalization.Raw | null;
        apply_language_text_normalization?: boolean | null;
    }
}

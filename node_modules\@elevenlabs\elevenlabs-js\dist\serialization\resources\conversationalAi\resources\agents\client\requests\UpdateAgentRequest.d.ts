/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../../index";
import * as ElevenLabs from "../../../../../../../api/index";
import * as core from "../../../../../../../core";
import { ConversationalConfig } from "../../../../../../types/ConversationalConfig";
import { AgentPlatformSettingsRequestModel } from "../../../../../../types/AgentPlatformSettingsRequestModel";
export declare const UpdateAgentRequest: core.serialization.Schema<serializers.conversationalAi.UpdateAgentRequest.Raw, ElevenLabs.conversationalAi.UpdateAgentRequest>;
export declare namespace UpdateAgentRequest {
    interface Raw {
        conversation_config?: ConversationalConfig.Raw | null;
        platform_settings?: AgentPlatformSettingsRequestModel.Raw | null;
        workflow?: unknown | null;
        name?: string | null;
        tags?: string[] | null;
    }
}

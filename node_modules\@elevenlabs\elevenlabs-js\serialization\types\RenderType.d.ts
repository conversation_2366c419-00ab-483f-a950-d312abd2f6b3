/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const RenderType: core.serialization.Schema<serializers.RenderType.Raw, ElevenLabs.RenderType>;
export declare namespace RenderType {
    type Raw = "mp4" | "aac" | "mp3" | "wav" | "aaf" | "tracks_zip" | "clips_zip";
}

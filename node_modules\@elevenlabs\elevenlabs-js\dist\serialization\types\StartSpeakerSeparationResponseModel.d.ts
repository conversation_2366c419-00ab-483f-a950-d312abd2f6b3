/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const StartSpeakerSeparationResponseModel: core.serialization.ObjectSchema<serializers.StartSpeakerSeparationResponseModel.Raw, ElevenLabs.StartSpeakerSeparationResponseModel>;
export declare namespace StartSpeakerSeparationResponseModel {
    interface Raw {
        status: string;
    }
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[agentId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useAgentConversation.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useAgentConversation.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAgentConversation: () => (/* binding */ useAgentConversation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var voice_stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! voice-stream */ \"(app-pages-browser)/../node_modules/voice-stream/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAgentConversation auto */ \n\nconst sendMessage = (websocket, request)=>{\n    if (websocket.readyState !== WebSocket.OPEN) {\n        return;\n    }\n    websocket.send(JSON.stringify(request));\n};\nconst useAgentConversation = (agentId)=>{\n    const websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [userTranscript, setUserTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [agentResponse, setAgentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isPlayingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const { startStreaming, stopStreaming } = (0,voice_stream__WEBPACK_IMPORTED_MODULE_1__.useVoiceStream)({\n        onAudioChunked: {\n            \"useAgentConversation.useVoiceStream\": (audioData)=>{\n                if (!websocketRef.current) return;\n                sendMessage(websocketRef.current, {\n                    user_audio_chunk: audioData\n                });\n            }\n        }[\"useAgentConversation.useVoiceStream\"]\n    });\n    // Initialize audio context\n    const initAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[initAudioContext]\": async ()=>{\n            if (!audioContextRef.current) {\n                try {\n                    const AudioContextClass = window.AudioContext || window.webkitAudioContext;\n                    audioContextRef.current = new AudioContextClass();\n                    // Resume audio context if it's suspended (required by some browsers)\n                    if (audioContextRef.current.state === 'suspended') {\n                        await audioContextRef.current.resume();\n                    }\n                } catch (error) {\n                    console.error('Failed to initialize audio context:', error);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[initAudioContext]\"], []);\n    // Play audio from base64 using HTML Audio element\n    const playAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[playAudio]\": async (base64Audio)=>{\n            try {\n                console.log('Attempting to play audio chunk, length:', base64Audio.length);\n                // Try different audio formats that ElevenLabs might use\n                const audioFormats = [\n                    'audio/mpeg',\n                    'audio/mp3',\n                    'audio/wav',\n                    'audio/ogg',\n                    'audio/webm'\n                ];\n                for (const format of audioFormats){\n                    try {\n                        const audio = new Audio(\"data:\".concat(format, \";base64,\").concat(base64Audio));\n                        audio.volume = 0.8;\n                        const playPromise = new Promise({\n                            \"useAgentConversation.useCallback[playAudio]\": (resolve, reject)=>{\n                                let resolved = false;\n                                const cleanup = {\n                                    \"useAgentConversation.useCallback[playAudio].cleanup\": ()=>{\n                                        audio.onended = null;\n                                        audio.onerror = null;\n                                        audio.oncanplaythrough = null;\n                                        audio.onloadeddata = null;\n                                    }\n                                }[\"useAgentConversation.useCallback[playAudio].cleanup\"];\n                                audio.onended = ({\n                                    \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                        if (!resolved) {\n                                            resolved = true;\n                                            cleanup();\n                                            console.log(\"Audio played successfully with format: \".concat(format));\n                                            resolve();\n                                        }\n                                    }\n                                })[\"useAgentConversation.useCallback[playAudio]\"];\n                                audio.onerror = ({\n                                    \"useAgentConversation.useCallback[playAudio]\": (error)=>{\n                                        if (!resolved) {\n                                            cleanup();\n                                            console.log(\"Failed to play with format \".concat(format, \":\"), error);\n                                            reject(error);\n                                        }\n                                    }\n                                })[\"useAgentConversation.useCallback[playAudio]\"];\n                                audio.oncanplaythrough = ({\n                                    \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                        console.log(\"Audio ready to play with format: \".concat(format));\n                                        audio.play().catch(reject);\n                                    }\n                                })[\"useAgentConversation.useCallback[playAudio]\"];\n                                // Fallback timeout\n                                setTimeout({\n                                    \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                        if (!resolved && audio.readyState >= 2) {\n                                            audio.play().catch(reject);\n                                        }\n                                    }\n                                }[\"useAgentConversation.useCallback[playAudio]\"], 200);\n                            }\n                        }[\"useAgentConversation.useCallback[playAudio]\"]);\n                        await playPromise;\n                        return; // Success, exit the loop\n                    } catch (formatError) {\n                        console.log(\"Format \".concat(format, \" failed, trying next...\"));\n                        continue;\n                    }\n                }\n                throw new Error('All audio formats failed');\n            } catch (error) {\n                console.error('Error playing audio with all formats:', error);\n            // Don't throw, just log and continue\n            }\n        }\n    }[\"useAgentConversation.useCallback[playAudio]\"], []);\n    // Process audio queue\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingRef.current || audioQueueRef.current.length === 0) return;\n            isPlayingRef.current = true;\n            const audioData = audioQueueRef.current.shift();\n            if (audioData) {\n                try {\n                    await playAudio(audioData);\n                } catch (error) {\n                    console.error('Error in processAudioQueue:', error);\n                // Continue processing even if one audio chunk fails\n                }\n            }\n            isPlayingRef.current = false;\n            // Process next audio in queue\n            if (audioQueueRef.current.length > 0) {\n                setTimeout({\n                    \"useAgentConversation.useCallback[processAudioQueue]\": ()=>processAudioQueue()\n                }[\"useAgentConversation.useCallback[processAudioQueue]\"], 50); // Small delay between chunks\n            }\n        }\n    }[\"useAgentConversation.useCallback[processAudioQueue]\"], [\n        playAudio\n    ]);\n    const startConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n            if (isConnected) return;\n            try {\n                // Request microphone permission\n                await navigator.mediaDevices.getUserMedia({\n                    audio: true\n                });\n                // Get signed URL for WebSocket connection\n                const response = await fetch(\"/api/conversation/signed-url?agentId=\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to get signed URL');\n                }\n                const { signed_url } = await response.json();\n                const websocket = new WebSocket(signed_url);\n                websocket.onopen = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        sendMessage(websocket, {\n                            type: \"conversation_initiation_client_data\"\n                        });\n                        await startStreaming();\n                        setIsListening(true);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onmessage = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async (event)=>{\n                        const data = JSON.parse(event.data);\n                        // Handle ping events to keep connection alive\n                        if (data.type === \"ping\") {\n                            setTimeout({\n                                \"useAgentConversation.useCallback[startConversation]\": ()=>{\n                                    sendMessage(websocket, {\n                                        type: \"pong\",\n                                        event_id: data.ping_event.event_id\n                                    });\n                                }\n                            }[\"useAgentConversation.useCallback[startConversation]\"], data.ping_event.ping_ms || 0);\n                        }\n                        if (data.type === \"user_transcript\") {\n                            const { user_transcription_event } = data;\n                            setUserTranscript(user_transcription_event.user_transcript);\n                            console.log(\"User transcript:\", user_transcription_event.user_transcript);\n                        }\n                        if (data.type === \"agent_response\") {\n                            const { agent_response_event } = data;\n                            setAgentResponse(agent_response_event.agent_response);\n                            console.log(\"Agent response:\", agent_response_event.agent_response);\n                        }\n                        if (data.type === \"interruption\") {\n                            console.log(\"Conversation interrupted\");\n                            // Clear audio queue on interruption\n                            audioQueueRef.current = [];\n                            isPlayingRef.current = false;\n                        }\n                        if (data.type === \"audio\") {\n                            const { audio_event } = data;\n                            console.log('Received audio chunk, length:', audio_event.audio_base_64.length);\n                            // Add audio to queue for sequential playback\n                            audioQueueRef.current.push(audio_event.audio_base_64);\n                            processAudioQueue();\n                        }\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocketRef.current = websocket;\n                websocket.onclose = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket disconnected');\n                        websocketRef.current = null;\n                        setIsConnected(false);\n                        setIsListening(false);\n                        stopStreaming();\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onerror = ({\n                    \"useAgentConversation.useCallback[startConversation]\": (error)=>{\n                        console.error('WebSocket error:', error);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                throw error;\n            }\n        }\n    }[\"useAgentConversation.useCallback[startConversation]\"], [\n        agentId,\n        isConnected,\n        startStreaming,\n        initAudioContext,\n        processAudioQueue\n    ]);\n    const stopConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[stopConversation]\": async ()=>{\n            if (!websocketRef.current) return;\n            websocketRef.current.close();\n            setUserTranscript('');\n            setAgentResponse('');\n            audioQueueRef.current = [];\n            isPlayingRef.current = false;\n        }\n    }[\"useAgentConversation.useCallback[stopConversation]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAgentConversation.useEffect\": ()=>{\n            return ({\n                \"useAgentConversation.useEffect\": ()=>{\n                    if (websocketRef.current) {\n                        websocketRef.current.close();\n                    }\n                }\n            })[\"useAgentConversation.useEffect\"];\n        }\n    }[\"useAgentConversation.useEffect\"], []);\n    return {\n        startConversation,\n        stopConversation,\n        isConnected,\n        isListening,\n        userTranscript,\n        agentResponse\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAgentConversation.ts\n"));

/***/ })

});
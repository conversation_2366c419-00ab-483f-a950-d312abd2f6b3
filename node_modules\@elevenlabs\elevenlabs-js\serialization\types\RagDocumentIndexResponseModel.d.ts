/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { EmbeddingModelEnum } from "./EmbeddingModelEnum";
import { RagIndexStatus } from "./RagIndexStatus";
import { RagDocumentIndexUsage } from "./RagDocumentIndexUsage";
export declare const RagDocumentIndexResponseModel: core.serialization.ObjectSchema<serializers.RagDocumentIndexResponseModel.Raw, ElevenLabs.RagDocumentIndexResponseModel>;
export declare namespace RagDocumentIndexResponseModel {
    interface Raw {
        id: string;
        model: EmbeddingModelEnum.Raw;
        status: RagIndexStatus.Raw;
        progress_percentage: number;
        document_model_index_usage: RagDocumentIndexUsage.Raw;
    }
}

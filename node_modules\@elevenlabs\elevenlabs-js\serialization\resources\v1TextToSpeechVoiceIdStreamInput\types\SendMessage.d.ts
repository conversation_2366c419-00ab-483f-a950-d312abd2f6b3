/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as ElevenLabs from "../../../../api/index";
import * as core from "../../../../core";
import { InitializeConnection } from "../../../types/InitializeConnection";
import { SendText } from "../../../types/SendText";
import { CloseConnection } from "../../../types/CloseConnection";
export declare const SendMessage: core.serialization.Schema<serializers.SendMessage.Raw, ElevenLabs.SendMessage>;
export declare namespace SendMessage {
    type Raw = InitializeConnection.Raw | SendText.Raw | CloseConnection.Raw;
}

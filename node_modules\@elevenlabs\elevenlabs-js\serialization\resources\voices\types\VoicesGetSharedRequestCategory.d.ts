/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as ElevenLabs from "../../../../api/index";
import * as core from "../../../../core";
export declare const VoicesGetSharedRequestCategory: core.serialization.Schema<serializers.VoicesGetSharedRequestCategory.Raw, ElevenLabs.VoicesGetSharedRequestCategory>;
export declare namespace VoicesGetSharedRequestCategory {
    type Raw = "professional" | "famous" | "high_quality";
}

var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var MockAudioContext = /** @class */ (function () {
    function class_1() {
        this.createMediaStreamSource = jest.fn();
        this.createScriptProcessor = jest.fn();
        this.sampleRate = 44100;
        this.close = jest.fn();
        this.destination = {};
    }
    return class_1;
}());
var MockMediaStream = /** @class */ (function () {
    function class_2() {
        this.getTracks = function () { return [{ stop: jest.fn() }]; };
    }
    return class_2;
}());
var MockAudioProcessingEvent = /** @class */ (function (_super) {
    __extends(class_3, _super);
    function class_3() {
        var _this = _super.call(this, "audioprocess") || this;
        _this.inputBuffer = {
            getChannelData: jest.fn(),
        };
        _this.outputBuffer = {
            getChannelData: jest.fn(),
        };
        _this.playbackTime = 0;
        return _this;
    }
    return class_3;
}(Event));
Object.defineProperty(window, "AudioContext", {
    writable: true,
    value: MockAudioContext,
});
Object.defineProperty(global.navigator, "mediaDevices", {
    writable: true,
    value: {
        getUserMedia: jest.fn(),
    },
});
Object.defineProperty(window, "AudioProcessingEvent", {
    writable: true,
    value: MockAudioProcessingEvent,
});
export {};

/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { EmbedVariant } from "./EmbedVariant";
import { WidgetPlacement } from "./WidgetPlacement";
import { WidgetExpandable } from "./WidgetExpandable";
import { WidgetConfigResponseModelAvatar } from "./WidgetConfigResponseModelAvatar";
import { WidgetFeedbackMode } from "./WidgetFeedbackMode";
import { WidgetTextContents } from "./WidgetTextContents";
import { WidgetStyles } from "./WidgetStyles";
import { WidgetLanguagePresetResponse } from "./WidgetLanguagePresetResponse";
export declare const WidgetConfigResponse: core.serialization.ObjectSchema<serializers.WidgetConfigResponse.Raw, ElevenLabs.WidgetConfigResponse>;
export declare namespace WidgetConfigResponse {
    interface Raw {
        variant?: EmbedVariant.Raw | null;
        placement?: WidgetPlacement.Raw | null;
        expandable?: WidgetExpandable.Raw | null;
        avatar?: WidgetConfigResponseModelAvatar.Raw | null;
        feedback_mode?: WidgetFeedbackMode.Raw | null;
        bg_color?: string | null;
        text_color?: string | null;
        btn_color?: string | null;
        btn_text_color?: string | null;
        border_color?: string | null;
        focus_color?: string | null;
        border_radius?: number | null;
        btn_radius?: number | null;
        action_text?: string | null;
        start_call_text?: string | null;
        end_call_text?: string | null;
        expand_text?: string | null;
        listening_text?: string | null;
        speaking_text?: string | null;
        shareable_page_text?: string | null;
        shareable_page_show_terms?: boolean | null;
        terms_text?: string | null;
        terms_html?: string | null;
        terms_key?: string | null;
        show_avatar_when_collapsed?: boolean | null;
        disable_banner?: boolean | null;
        override_link?: string | null;
        mic_muting_enabled?: boolean | null;
        transcript_enabled?: boolean | null;
        text_input_enabled?: boolean | null;
        text_contents?: WidgetTextContents.Raw | null;
        styles?: WidgetStyles.Raw | null;
        language: string;
        supported_language_overrides?: string[] | null;
        language_presets?: Record<string, WidgetLanguagePresetResponse.Raw> | null;
        text_only?: boolean | null;
        supports_text_only?: boolean | null;
        first_message?: string | null;
    }
}

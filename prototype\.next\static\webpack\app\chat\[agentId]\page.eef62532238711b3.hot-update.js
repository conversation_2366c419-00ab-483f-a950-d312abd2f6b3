"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[agentId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useAgentConversation.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useAgentConversation.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAgentConversation: () => (/* binding */ useAgentConversation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var voice_stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! voice-stream */ \"(app-pages-browser)/../node_modules/voice-stream/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAgentConversation auto */ \n\nconst sendMessage = (websocket, request)=>{\n    if (websocket.readyState !== WebSocket.OPEN) {\n        return;\n    }\n    websocket.send(JSON.stringify(request));\n};\nconst useAgentConversation = (agentId)=>{\n    const websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [userTranscript, setUserTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [agentResponse, setAgentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isPlayingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const { startStreaming, stopStreaming } = (0,voice_stream__WEBPACK_IMPORTED_MODULE_1__.useVoiceStream)({\n        onAudioChunked: {\n            \"useAgentConversation.useVoiceStream\": (audioData)=>{\n                if (!websocketRef.current) return;\n                sendMessage(websocketRef.current, {\n                    user_audio_chunk: audioData\n                });\n            }\n        }[\"useAgentConversation.useVoiceStream\"]\n    });\n    // Initialize audio context\n    const initAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[initAudioContext]\": async ()=>{\n            if (!audioContextRef.current) {\n                try {\n                    const AudioContextClass = window.AudioContext || window.webkitAudioContext;\n                    audioContextRef.current = new AudioContextClass();\n                    // Resume audio context if it's suspended (required by some browsers)\n                    if (audioContextRef.current.state === 'suspended') {\n                        await audioContextRef.current.resume();\n                    }\n                } catch (error) {\n                    console.error('Failed to initialize audio context:', error);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[initAudioContext]\"], []);\n    // Play audio from base64\n    const playAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[playAudio]\": async (base64Audio)=>{\n            if (!audioContextRef.current) return;\n            try {\n                // Method 1: Try using HTML Audio element (more compatible)\n                const audioBlob = new Blob([\n                    Uint8Array.from(atob(base64Audio), {\n                        \"useAgentConversation.useCallback[playAudio]\": (c)=>c.charCodeAt(0)\n                    }[\"useAgentConversation.useCallback[playAudio]\"])\n                ], {\n                    type: 'audio/mpeg'\n                });\n                const audioUrl = URL.createObjectURL(audioBlob);\n                const audio = new Audio(audioUrl);\n                return new Promise({\n                    \"useAgentConversation.useCallback[playAudio]\": (resolve, reject)=>{\n                        audio.onended = ({\n                            \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                URL.revokeObjectURL(audioUrl);\n                                resolve();\n                            }\n                        })[\"useAgentConversation.useCallback[playAudio]\"];\n                        audio.onerror = ({\n                            \"useAgentConversation.useCallback[playAudio]\": (error)=>{\n                                URL.revokeObjectURL(audioUrl);\n                                console.error('Audio playback error:', error);\n                                reject(error);\n                            }\n                        })[\"useAgentConversation.useCallback[playAudio]\"];\n                        audio.play().catch(reject);\n                    }\n                }[\"useAgentConversation.useCallback[playAudio]\"]);\n            } catch (error) {\n                console.error('Error playing audio:', error);\n                // Fallback: Try Web Audio API with different approach\n                try {\n                    const response = await fetch(\"data:audio/mpeg;base64,\".concat(base64Audio));\n                    const arrayBuffer = await response.arrayBuffer();\n                    const audioBuffer = await audioContextRef.current.decodeAudioData(arrayBuffer);\n                    const source = audioContextRef.current.createBufferSource();\n                    source.buffer = audioBuffer;\n                    source.connect(audioContextRef.current.destination);\n                    return new Promise({\n                        \"useAgentConversation.useCallback[playAudio]\": (resolve)=>{\n                            source.onended = ({\n                                \"useAgentConversation.useCallback[playAudio]\": ()=>resolve()\n                            })[\"useAgentConversation.useCallback[playAudio]\"];\n                            source.start();\n                        }\n                    }[\"useAgentConversation.useCallback[playAudio]\"]);\n                } catch (fallbackError) {\n                    console.error('Fallback audio playback also failed:', fallbackError);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[playAudio]\"], []);\n    // Process audio queue\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingRef.current || audioQueueRef.current.length === 0) return;\n            isPlayingRef.current = true;\n            const audioData = audioQueueRef.current.shift();\n            if (audioData) {\n                await playAudio(audioData);\n            }\n            isPlayingRef.current = false;\n            // Process next audio in queue\n            if (audioQueueRef.current.length > 0) {\n                processAudioQueue();\n            }\n        }\n    }[\"useAgentConversation.useCallback[processAudioQueue]\"], [\n        playAudio\n    ]);\n    const startConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n            if (isConnected) return;\n            try {\n                // Request microphone permission\n                await navigator.mediaDevices.getUserMedia({\n                    audio: true\n                });\n                // Initialize audio context\n                await initAudioContext();\n                // Get signed URL for WebSocket connection\n                const response = await fetch(\"/api/conversation/signed-url?agentId=\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to get signed URL');\n                }\n                const { signed_url } = await response.json();\n                const websocket = new WebSocket(signed_url);\n                websocket.onopen = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        sendMessage(websocket, {\n                            type: \"conversation_initiation_client_data\"\n                        });\n                        await startStreaming();\n                        setIsListening(true);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onmessage = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async (event)=>{\n                        const data = JSON.parse(event.data);\n                        // Handle ping events to keep connection alive\n                        if (data.type === \"ping\") {\n                            setTimeout({\n                                \"useAgentConversation.useCallback[startConversation]\": ()=>{\n                                    sendMessage(websocket, {\n                                        type: \"pong\",\n                                        event_id: data.ping_event.event_id\n                                    });\n                                }\n                            }[\"useAgentConversation.useCallback[startConversation]\"], data.ping_event.ping_ms || 0);\n                        }\n                        if (data.type === \"user_transcript\") {\n                            const { user_transcription_event } = data;\n                            setUserTranscript(user_transcription_event.user_transcript);\n                            console.log(\"User transcript:\", user_transcription_event.user_transcript);\n                        }\n                        if (data.type === \"agent_response\") {\n                            const { agent_response_event } = data;\n                            setAgentResponse(agent_response_event.agent_response);\n                            console.log(\"Agent response:\", agent_response_event.agent_response);\n                        }\n                        if (data.type === \"interruption\") {\n                            console.log(\"Conversation interrupted\");\n                            // Clear audio queue on interruption\n                            audioQueueRef.current = [];\n                            isPlayingRef.current = false;\n                        }\n                        if (data.type === \"audio\") {\n                            const { audio_event } = data;\n                            // Add audio to queue for sequential playback\n                            audioQueueRef.current.push(audio_event.audio_base_64);\n                            processAudioQueue();\n                        }\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocketRef.current = websocket;\n                websocket.onclose = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket disconnected');\n                        websocketRef.current = null;\n                        setIsConnected(false);\n                        setIsListening(false);\n                        stopStreaming();\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onerror = ({\n                    \"useAgentConversation.useCallback[startConversation]\": (error)=>{\n                        console.error('WebSocket error:', error);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                throw error;\n            }\n        }\n    }[\"useAgentConversation.useCallback[startConversation]\"], [\n        agentId,\n        isConnected,\n        startStreaming,\n        initAudioContext,\n        processAudioQueue\n    ]);\n    const stopConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[stopConversation]\": async ()=>{\n            if (!websocketRef.current) return;\n            websocketRef.current.close();\n            setUserTranscript('');\n            setAgentResponse('');\n            audioQueueRef.current = [];\n            isPlayingRef.current = false;\n        }\n    }[\"useAgentConversation.useCallback[stopConversation]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAgentConversation.useEffect\": ()=>{\n            return ({\n                \"useAgentConversation.useEffect\": ()=>{\n                    if (websocketRef.current) {\n                        websocketRef.current.close();\n                    }\n                }\n            })[\"useAgentConversation.useEffect\"];\n        }\n    }[\"useAgentConversation.useEffect\"], []);\n    return {\n        startConversation,\n        stopConversation,\n        isConnected,\n        isListening,\n        userTranscript,\n        agentResponse\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAgentConversation.ts\n"));

/***/ })

});
/**
 * Map of images extensions to MIME types
 */ "use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "imageExtMimeTypeMap", {
    enumerable: true,
    get: function() {
        return imageExtMimeTypeMap;
    }
});
const imageExtMimeTypeMap = {
    jpeg: 'image/jpeg',
    png: 'image/png',
    ico: 'image/x-icon',
    svg: 'image/svg+xml'
};

//# sourceMappingURL=mime-type.js.map
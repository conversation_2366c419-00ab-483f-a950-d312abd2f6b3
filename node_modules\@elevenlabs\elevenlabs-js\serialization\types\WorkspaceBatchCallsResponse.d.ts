/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { BatchCallResponse } from "./BatchCallResponse";
export declare const WorkspaceBatchCallsResponse: core.serialization.ObjectSchema<serializers.WorkspaceBatchCallsResponse.Raw, ElevenLabs.WorkspaceBatchCallsResponse>;
export declare namespace WorkspaceBatchCallsResponse {
    interface Raw {
        batch_calls: BatchCallResponse.Raw[];
        next_doc?: string | null;
        has_more?: boolean | null;
    }
}

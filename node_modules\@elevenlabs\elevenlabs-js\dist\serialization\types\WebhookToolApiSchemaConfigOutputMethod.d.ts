/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const WebhookToolApiSchemaConfigOutputMethod: core.serialization.Schema<serializers.WebhookToolApiSchemaConfigOutputMethod.Raw, ElevenLabs.WebhookToolApiSchemaConfigOutputMethod>;
export declare namespace WebhookToolApiSchemaConfigOutputMethod {
    type Raw = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";
}

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SpeechHistoryItemResponseModelSource: core.serialization.Schema<serializers.SpeechHistoryItemResponseModelSource.Raw, ElevenLabs.SpeechHistoryItemResponseModelSource>;
export declare namespace SpeechHistoryItemResponseModelSource {
    type Raw = "TTS" | "STS" | "Projects" | "PD" | "AN" | "Dubbing" | "PlayAPI" | "ConvAI";
}

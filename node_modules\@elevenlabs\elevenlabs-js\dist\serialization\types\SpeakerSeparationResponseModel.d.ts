/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { SpeakerSeparationResponseModelStatus } from "./SpeakerSeparationResponseModelStatus";
import { SpeakerResponseModel } from "./SpeakerResponseModel";
export declare const SpeakerSeparationResponseModel: core.serialization.ObjectSchema<serializers.SpeakerSeparationResponseModel.Raw, ElevenLabs.SpeakerSeparationResponseModel>;
export declare namespace SpeakerSeparationResponseModel {
    interface Raw {
        voice_id: string;
        sample_id: string;
        status: SpeakerSeparationResponseModelStatus.Raw;
        speakers?: Record<string, SpeakerResponseModel.Raw | null | undefined> | null;
        selected_speaker_ids?: string[] | null;
    }
}

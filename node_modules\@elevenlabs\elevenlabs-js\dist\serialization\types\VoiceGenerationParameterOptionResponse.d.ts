/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const VoiceGenerationParameterOptionResponse: core.serialization.ObjectSchema<serializers.VoiceGenerationParameterOptionResponse.Raw, ElevenLabs.VoiceGenerationParameterOptionResponse>;
export declare namespace VoiceGenerationParameterOptionResponse {
    interface Raw {
        name: string;
        code: string;
    }
}

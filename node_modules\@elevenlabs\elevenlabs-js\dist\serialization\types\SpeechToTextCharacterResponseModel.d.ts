/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SpeechToTextCharacterResponseModel: core.serialization.ObjectSchema<serializers.SpeechToTextCharacterResponseModel.Raw, ElevenLabs.SpeechToTextCharacterResponseModel>;
export declare namespace SpeechToTextCharacterResponseModel {
    interface Raw {
        text: string;
        start?: number | null;
        end?: number | null;
    }
}

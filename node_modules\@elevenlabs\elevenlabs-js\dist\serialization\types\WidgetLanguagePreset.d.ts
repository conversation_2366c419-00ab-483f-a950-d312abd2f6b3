/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { WidgetTextContents } from "./WidgetTextContents";
export declare const WidgetLanguagePreset: core.serialization.ObjectSchema<serializers.WidgetLanguagePreset.Raw, ElevenLabs.WidgetLanguagePreset>;
export declare namespace WidgetLanguagePreset {
    interface Raw {
        text_contents?: WidgetTextContents.Raw | null;
    }
}

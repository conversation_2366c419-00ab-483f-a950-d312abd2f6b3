"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/voice-stream";
exports.ids = ["vendor-chunks/voice-stream"];
exports.modules = {

/***/ "(ssr)/../node_modules/voice-stream/dist/constants/voice-stream.constants.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/voice-stream/dist/constants/voice-stream.constants.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_BUFFER_SIZE: () => (/* binding */ DEFAULT_BUFFER_SIZE),\n/* harmony export */   DEFAULT_INCLUDE_DESTINATION: () => (/* binding */ DEFAULT_INCLUDE_DESTINATION),\n/* harmony export */   DEFAULT_SILENCE_DURATION: () => (/* binding */ DEFAULT_SILENCE_DURATION),\n/* harmony export */   DEFAULT_SILENCE_THRESHOLD: () => (/* binding */ DEFAULT_SILENCE_THRESHOLD),\n/* harmony export */   DEFAULT_TARGET_SAMPLE_RATE: () => (/* binding */ DEFAULT_TARGET_SAMPLE_RATE)\n/* harmony export */ });\nvar DEFAULT_TARGET_SAMPLE_RATE = 16000;\nvar DEFAULT_BUFFER_SIZE = 8192;\nvar DEFAULT_SILENCE_THRESHOLD = -50; // dB\nvar DEFAULT_SILENCE_DURATION = 1000; // ms\nvar DEFAULT_INCLUDE_DESTINATION = true;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3ZvaWNlLXN0cmVhbS9kaXN0L2NvbnN0YW50cy92b2ljZS1zdHJlYW0uY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQU87QUFDQTtBQUNBLHFDQUFxQztBQUNyQyxxQ0FBcUM7QUFDckMiLCJzb3VyY2VzIjpbIkU6XFxwcm90b3R5cGUgbWFzIG11amlcXG5vZGVfbW9kdWxlc1xcdm9pY2Utc3RyZWFtXFxkaXN0XFxjb25zdGFudHNcXHZvaWNlLXN0cmVhbS5jb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBERUZBVUxUX1RBUkdFVF9TQU1QTEVfUkFURSA9IDE2MDAwO1xuZXhwb3J0IHZhciBERUZBVUxUX0JVRkZFUl9TSVpFID0gODE5MjtcbmV4cG9ydCB2YXIgREVGQVVMVF9TSUxFTkNFX1RIUkVTSE9MRCA9IC01MDsgLy8gZEJcbmV4cG9ydCB2YXIgREVGQVVMVF9TSUxFTkNFX0RVUkFUSU9OID0gMTAwMDsgLy8gbXNcbmV4cG9ydCB2YXIgREVGQVVMVF9JTkNMVURFX0RFU1RJTkFUSU9OID0gdHJ1ZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/voice-stream/dist/constants/voice-stream.constants.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/voice-stream/dist/hooks/use-voice-stream.js":
/*!*******************************************************************!*\
  !*** ../node_modules/voice-stream/dist/hooks/use-voice-stream.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useVoiceStream: () => (/* binding */ useVoiceStream)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_downsample_buffer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/downsample-buffer */ \"(ssr)/../node_modules/voice-stream/dist/utils/downsample-buffer.js\");\n/* harmony import */ var _utils_int16array_to_base64__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/int16array-to-base64 */ \"(ssr)/../node_modules/voice-stream/dist/utils/int16array-to-base64.js\");\n/* harmony import */ var _utils_silence_detection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/silence-detection */ \"(ssr)/../node_modules/voice-stream/dist/utils/silence-detection.js\");\n/* harmony import */ var _constants_voice_stream_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../constants/voice-stream.constants */ \"(ssr)/../node_modules/voice-stream/dist/constants/voice-stream.constants.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (undefined && undefined.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\n\n\n\n\n\nvar useVoiceStream = function (options) {\n    var onStartStreaming = options.onStartStreaming, onStopStreaming = options.onStopStreaming, onAudioChunked = options.onAudioChunked, onError = options.onError, _a = options.targetSampleRate, targetSampleRate = _a === void 0 ? _constants_voice_stream_constants__WEBPACK_IMPORTED_MODULE_4__.DEFAULT_TARGET_SAMPLE_RATE : _a, _b = options.bufferSize, bufferSize = _b === void 0 ? _constants_voice_stream_constants__WEBPACK_IMPORTED_MODULE_4__.DEFAULT_BUFFER_SIZE : _b, _c = options.enableSilenceDetection, enableSilenceDetection = _c === void 0 ? false : _c, _d = options.silenceThreshold, silenceThreshold = _d === void 0 ? _constants_voice_stream_constants__WEBPACK_IMPORTED_MODULE_4__.DEFAULT_SILENCE_THRESHOLD : _d, _e = options.silenceDuration, silenceDuration = _e === void 0 ? _constants_voice_stream_constants__WEBPACK_IMPORTED_MODULE_4__.DEFAULT_SILENCE_DURATION : _e, _f = options.autoStopOnSilence, autoStopOnSilence = _f === void 0 ? false : _f, _g = options.includeDestination, includeDestination = _g === void 0 ? _constants_voice_stream_constants__WEBPACK_IMPORTED_MODULE_4__.DEFAULT_INCLUDE_DESTINATION : _g;\n    var _h = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false), isStreaming = _h[0], setIsStreaming = _h[1];\n    var audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var mediaStreamRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var scriptProcessorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var sourceNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var silenceDetectorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var handleError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (error) {\n        console.error(\"Voice stream error:\", error);\n        if (onError) {\n            onError(error);\n        }\n    }, [onError]);\n    var stopStreaming = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n        if (!isStreaming)\n            return;\n        if (scriptProcessorRef.current) {\n            scriptProcessorRef.current.disconnect();\n            scriptProcessorRef.current.onaudioprocess = null;\n            scriptProcessorRef.current = null;\n        }\n        if (sourceNodeRef.current) {\n            sourceNodeRef.current.disconnect();\n            sourceNodeRef.current = null;\n        }\n        if (audioContextRef.current) {\n            audioContextRef.current.close();\n            audioContextRef.current = null;\n        }\n        if (mediaStreamRef.current) {\n            mediaStreamRef.current.getTracks().forEach(function (track) { return track.stop(); });\n            mediaStreamRef.current = null;\n        }\n        if (silenceDetectorRef.current) {\n            silenceDetectorRef.current.reset();\n            silenceDetectorRef.current = null;\n        }\n        setIsStreaming(false);\n        if (onStopStreaming) {\n            onStopStreaming();\n        }\n    }, [isStreaming, onStopStreaming]);\n    var startStreaming = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () { return __awaiter(void 0, void 0, void 0, function () {\n        var stream, audioContext, sampleRate_1, sourceNode, scriptProcessor, error_1;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    if (isStreaming)\n                        return [2 /*return*/];\n                    _a.label = 1;\n                case 1:\n                    _a.trys.push([1, 3, , 4]);\n                    return [4 /*yield*/, navigator.mediaDevices.getUserMedia({ audio: true })];\n                case 2:\n                    stream = _a.sent();\n                    mediaStreamRef.current = stream;\n                    audioContext = new window.AudioContext();\n                    audioContextRef.current = audioContext;\n                    sampleRate_1 = audioContext.sampleRate;\n                    sourceNode = audioContext.createMediaStreamSource(stream);\n                    sourceNodeRef.current = sourceNode;\n                    scriptProcessor = audioContext.createScriptProcessor(bufferSize, 1, 1);\n                    scriptProcessorRef.current = scriptProcessor;\n                    // Initialize silence detector if enabled\n                    if (enableSilenceDetection) {\n                        silenceDetectorRef.current = new _utils_silence_detection__WEBPACK_IMPORTED_MODULE_3__.SilenceDetector(silenceThreshold, silenceDuration, autoStopOnSilence ? stopStreaming : undefined);\n                    }\n                    scriptProcessor.onaudioprocess = function (audioProcessingEvent) {\n                        try {\n                            var inputBuffer = audioProcessingEvent.inputBuffer;\n                            var channelData = inputBuffer.getChannelData(0);\n                            // Process silence detection if enabled\n                            if (enableSilenceDetection && silenceDetectorRef.current) {\n                                silenceDetectorRef.current.processAudioData(channelData);\n                            }\n                            var downsampledBuffer = (0,_utils_downsample_buffer__WEBPACK_IMPORTED_MODULE_1__.downsampleBuffer)(channelData, sampleRate_1, targetSampleRate);\n                            var base64Data = (0,_utils_int16array_to_base64__WEBPACK_IMPORTED_MODULE_2__.int16ArrayToBase64)(downsampledBuffer);\n                            if (onAudioChunked) {\n                                onAudioChunked(base64Data);\n                            }\n                        }\n                        catch (error) {\n                            handleError(error instanceof Error ? error : new Error(String(error)));\n                        }\n                    };\n                    sourceNode.connect(scriptProcessor);\n                    if (includeDestination) {\n                        scriptProcessor.connect(audioContext.destination);\n                    }\n                    setIsStreaming(true);\n                    if (onStartStreaming) {\n                        onStartStreaming();\n                    }\n                    return [3 /*break*/, 4];\n                case 3:\n                    error_1 = _a.sent();\n                    handleError(error_1 instanceof Error ? error_1 : new Error(String(error_1)));\n                    return [3 /*break*/, 4];\n                case 4: return [2 /*return*/];\n            }\n        });\n    }); }, [\n        isStreaming,\n        onStartStreaming,\n        onAudioChunked,\n        onError,\n        targetSampleRate,\n        bufferSize,\n        enableSilenceDetection,\n        silenceThreshold,\n        silenceDuration,\n        autoStopOnSilence,\n        includeDestination,\n        stopStreaming,\n        handleError,\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        return function () {\n            stopStreaming();\n        };\n    }, [stopStreaming]);\n    return {\n        startStreaming: startStreaming,\n        stopStreaming: stopStreaming,\n        isStreaming: isStreaming,\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/voice-stream/dist/hooks/use-voice-stream.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/voice-stream/dist/index.js":
/*!**************************************************!*\
  !*** ../node_modules/voice-stream/dist/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useVoiceStream: () => (/* reexport safe */ _hooks_use_voice_stream__WEBPACK_IMPORTED_MODULE_0__.useVoiceStream)\n/* harmony export */ });\n/* harmony import */ var _hooks_use_voice_stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hooks/use-voice-stream */ \"(ssr)/../node_modules/voice-stream/dist/hooks/use-voice-stream.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3ZvaWNlLXN0cmVhbS9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBEIiwic291cmNlcyI6WyJFOlxccHJvdG90eXBlIG1hcyBtdWppXFxub2RlX21vZHVsZXNcXHZvaWNlLXN0cmVhbVxcZGlzdFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgdXNlVm9pY2VTdHJlYW0gfSBmcm9tIFwiLi9ob29rcy91c2Utdm9pY2Utc3RyZWFtXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/voice-stream/dist/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/voice-stream/dist/utils/downsample-buffer.js":
/*!********************************************************************!*\
  !*** ../node_modules/voice-stream/dist/utils/downsample-buffer.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   downsampleBuffer: () => (/* binding */ downsampleBuffer)\n/* harmony export */ });\nvar downsampleBuffer = function (buffer, sampleRate, outSampleRate) {\n    if (outSampleRate === sampleRate) {\n        var result_1 = new Int16Array(buffer.length);\n        for (var i = 0; i < buffer.length; i++) {\n            var s = Math.max(-1, Math.min(1, buffer[i]));\n            result_1[i] = s < 0 ? s * 0x8000 : s * 0x7fff;\n        }\n        return result_1;\n    }\n    var sampleRateRatio = sampleRate / outSampleRate;\n    var newLength = Math.round(buffer.length / sampleRateRatio);\n    var result = new Int16Array(newLength);\n    var offsetResult = 0;\n    var offsetBuffer = 0;\n    while (offsetResult < newLength) {\n        var nextOffsetBuffer = Math.round((offsetResult + 1) * sampleRateRatio);\n        var accum = 0;\n        var count = 0;\n        for (var i = offsetBuffer; i < nextOffsetBuffer && i < buffer.length; i++) {\n            accum += buffer[i];\n            count++;\n        }\n        var avg = accum / count;\n        var s = Math.max(-1, Math.min(1, avg));\n        result[offsetResult] = s < 0 ? s * 0x8000 : s * 0x7fff;\n        offsetResult++;\n        offsetBuffer = nextOffsetBuffer;\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/voice-stream/dist/utils/downsample-buffer.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/voice-stream/dist/utils/int16array-to-base64.js":
/*!***********************************************************************!*\
  !*** ../node_modules/voice-stream/dist/utils/int16array-to-base64.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   int16ArrayToBase64: () => (/* binding */ int16ArrayToBase64)\n/* harmony export */ });\nvar int16ArrayToBase64 = function (buffer) {\n    var binary = \"\";\n    var bytes = new Uint8Array(buffer.buffer);\n    for (var i = 0; i < bytes.byteLength; i++) {\n        binary += String.fromCharCode(bytes[i]);\n    }\n    return btoa(binary);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL3ZvaWNlLXN0cmVhbS9kaXN0L3V0aWxzL2ludDE2YXJyYXktdG8tYmFzZTY0LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQSxvQkFBb0Isc0JBQXNCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJFOlxccHJvdG90eXBlIG1hcyBtdWppXFxub2RlX21vZHVsZXNcXHZvaWNlLXN0cmVhbVxcZGlzdFxcdXRpbHNcXGludDE2YXJyYXktdG8tYmFzZTY0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB2YXIgaW50MTZBcnJheVRvQmFzZTY0ID0gZnVuY3Rpb24gKGJ1ZmZlcikge1xuICAgIHZhciBiaW5hcnkgPSBcIlwiO1xuICAgIHZhciBieXRlcyA9IG5ldyBVaW50OEFycmF5KGJ1ZmZlci5idWZmZXIpO1xuICAgIGZvciAodmFyIGkgPSAwOyBpIDwgYnl0ZXMuYnl0ZUxlbmd0aDsgaSsrKSB7XG4gICAgICAgIGJpbmFyeSArPSBTdHJpbmcuZnJvbUNoYXJDb2RlKGJ5dGVzW2ldKTtcbiAgICB9XG4gICAgcmV0dXJuIGJ0b2EoYmluYXJ5KTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/voice-stream/dist/utils/int16array-to-base64.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/voice-stream/dist/utils/silence-detection.js":
/*!********************************************************************!*\
  !*** ../node_modules/voice-stream/dist/utils/silence-detection.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SilenceDetector: () => (/* binding */ SilenceDetector)\n/* harmony export */ });\nvar SilenceDetector = /** @class */ (function () {\n    function SilenceDetector(threshold, duration, onSilenceDetected) {\n        this.silenceStartTime = null;\n        this.threshold = threshold;\n        this.duration = duration;\n        this.onSilenceDetected = onSilenceDetected;\n    }\n    SilenceDetector.prototype.processAudioData = function (channelData) {\n        // Calculate RMS value\n        var sum = 0;\n        for (var i = 0; i < channelData.length; i++) {\n            sum += channelData[i] * channelData[i];\n        }\n        var rms = Math.sqrt(sum / channelData.length);\n        var db = 20 * Math.log10(rms);\n        // Check if audio is below threshold\n        if (db < this.threshold) {\n            if (this.silenceStartTime === null) {\n                this.silenceStartTime = Date.now();\n            }\n            else if (Date.now() - this.silenceStartTime >= this.duration &&\n                this.onSilenceDetected) {\n                this.onSilenceDetected();\n                return true;\n            }\n        }\n        else {\n            this.silenceStartTime = null;\n        }\n        return false;\n    };\n    SilenceDetector.prototype.reset = function () {\n        this.silenceStartTime = null;\n    };\n    return SilenceDetector;\n}());\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/voice-stream/dist/utils/silence-detection.js\n");

/***/ })

};
;
/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const VoiceSamplePreviewResponseModel: core.serialization.ObjectSchema<serializers.VoiceSamplePreviewResponseModel.Raw, ElevenLabs.VoiceSamplePreviewResponseModel>;
export declare namespace VoiceSamplePreviewResponseModel {
    interface Raw {
        audio_base_64: string;
        voice_id: string;
        sample_id: string;
        media_type: string;
        duration_secs?: number | null;
    }
}

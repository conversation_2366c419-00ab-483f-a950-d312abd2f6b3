/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const WebhookAuthMethodType: core.serialization.Schema<serializers.WebhookAuthMethodType.Raw, ElevenLabs.WebhookAuthMethodType>;
export declare namespace WebhookAuthMethodType {
    type Raw = "hmac" | "oauth2";
}

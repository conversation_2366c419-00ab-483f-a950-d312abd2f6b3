/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const WidgetStyles: core.serialization.ObjectSchema<serializers.WidgetStyles.Raw, ElevenLabs.WidgetStyles>;
export declare namespace WidgetStyles {
    interface Raw {
        base?: string | null;
        base_hover?: string | null;
        base_active?: string | null;
        base_border?: string | null;
        base_subtle?: string | null;
        base_primary?: string | null;
        base_error?: string | null;
        accent?: string | null;
        accent_hover?: string | null;
        accent_active?: string | null;
        accent_border?: string | null;
        accent_subtle?: string | null;
        accent_primary?: string | null;
        overlay_padding?: number | null;
        button_radius?: number | null;
        input_radius?: number | null;
        bubble_radius?: number | null;
        sheet_radius?: number | null;
        compact_sheet_radius?: number | null;
        dropdown_sheet_radius?: number | null;
    }
}

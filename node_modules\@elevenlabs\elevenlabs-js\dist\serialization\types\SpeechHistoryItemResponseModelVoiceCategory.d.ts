/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SpeechHistoryItemResponseModelVoiceCategory: core.serialization.Schema<serializers.SpeechHistoryItemResponseModelVoiceCategory.Raw, ElevenLabs.SpeechHistoryItemResponseModelVoiceCategory>;
export declare namespace SpeechHistoryItemResponseModelVoiceCategory {
    type Raw = "premade" | "cloned" | "generated" | "professional";
}

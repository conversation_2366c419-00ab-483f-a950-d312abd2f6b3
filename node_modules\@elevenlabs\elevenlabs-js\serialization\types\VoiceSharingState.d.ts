/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const VoiceSharingState: core.serialization.Schema<serializers.VoiceSharingState.Raw, ElevenLabs.VoiceSharingState>;
export declare namespace VoiceSharingState {
    type Raw = "enabled" | "disabled" | "copied" | "copied_disabled";
}

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../../index";
import * as ElevenLabs from "../../../../../../../api/index";
import * as core from "../../../../../../../core";
export declare const RemovePronunciationDictionaryRulesRequest: core.serialization.Schema<serializers.pronunciationDictionaries.RemovePronunciationDictionaryRulesRequest.Raw, ElevenLabs.pronunciationDictionaries.RemovePronunciationDictionaryRulesRequest>;
export declare namespace RemovePronunciationDictionaryRulesRequest {
    interface Raw {
        rule_strings: string[];
    }
}

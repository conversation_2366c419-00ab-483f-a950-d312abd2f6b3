/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { SafetyEvaluation } from "./SafetyEvaluation";
export declare const SafetyCommonModel: core.serialization.ObjectSchema<serializers.SafetyCommonModel.Raw, ElevenLabs.SafetyCommonModel>;
export declare namespace SafetyCommonModel {
    interface Raw {
        ivc?: SafetyEvaluation.Raw | null;
        non_ivc?: SafetyEvaluation.Raw | null;
    }
}

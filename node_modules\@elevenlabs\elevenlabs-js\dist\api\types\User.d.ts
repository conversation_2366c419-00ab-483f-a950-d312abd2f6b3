/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface User {
    /** The unique identifier of the user. */
    userId: string;
    /** Details of the user's subscription. */
    subscription: ElevenLabs.SubscriptionResponse;
    /** Optional additional details about the user's subscription. */
    subscriptionExtras?: ElevenLabs.SubscriptionExtrasResponseModel;
    /** Whether the user is new. */
    isNewUser: boolean;
    /** The API key of the user. */
    xiApiKey?: string;
    /** This field is deprecated and will be removed in a future major version. Instead use subscription.trust_on_invoice_creation. */
    canUseDelayedPaymentMethods: boolean;
    /** Whether the user's onboarding is completed. */
    isOnboardingCompleted: boolean;
    /** Whether the user's onboarding checklist is completed. */
    isOnboardingChecklistCompleted: boolean;
    /** First name of the user. */
    firstName?: string;
    /** Whether the user's API key is hashed. */
    isApiKeyHashed?: boolean;
    /** The preview of the user's API key. */
    xiApiKeyPreview?: string;
    /** The referral link code of the user. */
    referralLinkCode?: string;
    /** The Partnerstack partner default link of the user. */
    partnerstackPartnerDefaultLink?: string;
}

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const WidgetFeedbackMode: core.serialization.Schema<serializers.WidgetFeedbackMode.Raw, ElevenLabs.WidgetFeedbackMode>;
export declare namespace WidgetFeedbackMode {
    type Raw = "none" | "during" | "end";
}

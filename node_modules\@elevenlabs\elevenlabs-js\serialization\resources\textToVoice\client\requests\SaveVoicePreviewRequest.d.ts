/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../index";
import * as ElevenLabs from "../../../../../api/index";
import * as core from "../../../../../core";
export declare const SaveVoicePreviewRequest: core.serialization.Schema<serializers.SaveVoicePreviewRequest.Raw, ElevenLabs.SaveVoicePreviewRequest>;
export declare namespace SaveVoicePreviewRequest {
    interface Raw {
        voice_name: string;
        voice_description: string;
        generated_voice_id: string;
        labels?: Record<string, string | null | undefined> | null;
        played_not_selected_voice_ids?: string[] | null;
    }
}

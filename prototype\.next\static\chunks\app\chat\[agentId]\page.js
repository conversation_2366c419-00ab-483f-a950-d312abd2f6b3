/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/chat/[agentId]/page"],{

/***/ "(app-pages-browser)/../node_modules/voice-stream/dist/constants/voice-stream.constants.js":
/*!*****************************************************************************!*\
  !*** ../node_modules/voice-stream/dist/constants/voice-stream.constants.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_BUFFER_SIZE: () => (/* binding */ DEFAULT_BUFFER_SIZE),\n/* harmony export */   DEFAULT_INCLUDE_DESTINATION: () => (/* binding */ DEFAULT_INCLUDE_DESTINATION),\n/* harmony export */   DEFAULT_SILENCE_DURATION: () => (/* binding */ DEFAULT_SILENCE_DURATION),\n/* harmony export */   DEFAULT_SILENCE_THRESHOLD: () => (/* binding */ DEFAULT_SILENCE_THRESHOLD),\n/* harmony export */   DEFAULT_TARGET_SAMPLE_RATE: () => (/* binding */ DEFAULT_TARGET_SAMPLE_RATE)\n/* harmony export */ });\nvar DEFAULT_TARGET_SAMPLE_RATE = 16000;\nvar DEFAULT_BUFFER_SIZE = 8192;\nvar DEFAULT_SILENCE_THRESHOLD = -50; // dB\nvar DEFAULT_SILENCE_DURATION = 1000; // ms\nvar DEFAULT_INCLUDE_DESTINATION = true;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvdm9pY2Utc3RyZWFtL2Rpc3QvY29uc3RhbnRzL3ZvaWNlLXN0cmVhbS5jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBTztBQUNBO0FBQ0EscUNBQXFDO0FBQ3JDLHFDQUFxQztBQUNyQyIsInNvdXJjZXMiOlsiRTpcXHByb3RvdHlwZSBtYXMgbXVqaVxcbm9kZV9tb2R1bGVzXFx2b2ljZS1zdHJlYW1cXGRpc3RcXGNvbnN0YW50c1xcdm9pY2Utc3RyZWFtLmNvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIERFRkFVTFRfVEFSR0VUX1NBTVBMRV9SQVRFID0gMTYwMDA7XG5leHBvcnQgdmFyIERFRkFVTFRfQlVGRkVSX1NJWkUgPSA4MTkyO1xuZXhwb3J0IHZhciBERUZBVUxUX1NJTEVOQ0VfVEhSRVNIT0xEID0gLTUwOyAvLyBkQlxuZXhwb3J0IHZhciBERUZBVUxUX1NJTEVOQ0VfRFVSQVRJT04gPSAxMDAwOyAvLyBtc1xuZXhwb3J0IHZhciBERUZBVUxUX0lOQ0xVREVfREVTVElOQVRJT04gPSB0cnVlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/voice-stream/dist/constants/voice-stream.constants.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/voice-stream/dist/hooks/use-voice-stream.js":
/*!*******************************************************************!*\
  !*** ../node_modules/voice-stream/dist/hooks/use-voice-stream.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useVoiceStream: () => (/* binding */ useVoiceStream)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_downsample_buffer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/downsample-buffer */ \"(app-pages-browser)/../node_modules/voice-stream/dist/utils/downsample-buffer.js\");\n/* harmony import */ var _utils_int16array_to_base64__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/int16array-to-base64 */ \"(app-pages-browser)/../node_modules/voice-stream/dist/utils/int16array-to-base64.js\");\n/* harmony import */ var _utils_silence_detection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/silence-detection */ \"(app-pages-browser)/../node_modules/voice-stream/dist/utils/silence-detection.js\");\n/* harmony import */ var _constants_voice_stream_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../constants/voice-stream.constants */ \"(app-pages-browser)/../node_modules/voice-stream/dist/constants/voice-stream.constants.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (undefined && undefined.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n    return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\n\n\n\n\n\nvar useVoiceStream = function (options) {\n    var onStartStreaming = options.onStartStreaming, onStopStreaming = options.onStopStreaming, onAudioChunked = options.onAudioChunked, onError = options.onError, _a = options.targetSampleRate, targetSampleRate = _a === void 0 ? _constants_voice_stream_constants__WEBPACK_IMPORTED_MODULE_4__.DEFAULT_TARGET_SAMPLE_RATE : _a, _b = options.bufferSize, bufferSize = _b === void 0 ? _constants_voice_stream_constants__WEBPACK_IMPORTED_MODULE_4__.DEFAULT_BUFFER_SIZE : _b, _c = options.enableSilenceDetection, enableSilenceDetection = _c === void 0 ? false : _c, _d = options.silenceThreshold, silenceThreshold = _d === void 0 ? _constants_voice_stream_constants__WEBPACK_IMPORTED_MODULE_4__.DEFAULT_SILENCE_THRESHOLD : _d, _e = options.silenceDuration, silenceDuration = _e === void 0 ? _constants_voice_stream_constants__WEBPACK_IMPORTED_MODULE_4__.DEFAULT_SILENCE_DURATION : _e, _f = options.autoStopOnSilence, autoStopOnSilence = _f === void 0 ? false : _f, _g = options.includeDestination, includeDestination = _g === void 0 ? _constants_voice_stream_constants__WEBPACK_IMPORTED_MODULE_4__.DEFAULT_INCLUDE_DESTINATION : _g;\n    var _h = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false), isStreaming = _h[0], setIsStreaming = _h[1];\n    var audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var mediaStreamRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var scriptProcessorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var sourceNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var silenceDetectorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var handleError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (error) {\n        console.error(\"Voice stream error:\", error);\n        if (onError) {\n            onError(error);\n        }\n    }, [onError]);\n    var stopStreaming = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n        if (!isStreaming)\n            return;\n        if (scriptProcessorRef.current) {\n            scriptProcessorRef.current.disconnect();\n            scriptProcessorRef.current.onaudioprocess = null;\n            scriptProcessorRef.current = null;\n        }\n        if (sourceNodeRef.current) {\n            sourceNodeRef.current.disconnect();\n            sourceNodeRef.current = null;\n        }\n        if (audioContextRef.current) {\n            audioContextRef.current.close();\n            audioContextRef.current = null;\n        }\n        if (mediaStreamRef.current) {\n            mediaStreamRef.current.getTracks().forEach(function (track) { return track.stop(); });\n            mediaStreamRef.current = null;\n        }\n        if (silenceDetectorRef.current) {\n            silenceDetectorRef.current.reset();\n            silenceDetectorRef.current = null;\n        }\n        setIsStreaming(false);\n        if (onStopStreaming) {\n            onStopStreaming();\n        }\n    }, [isStreaming, onStopStreaming]);\n    var startStreaming = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () { return __awaiter(void 0, void 0, void 0, function () {\n        var stream, audioContext, sampleRate_1, sourceNode, scriptProcessor, error_1;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    if (isStreaming)\n                        return [2 /*return*/];\n                    _a.label = 1;\n                case 1:\n                    _a.trys.push([1, 3, , 4]);\n                    return [4 /*yield*/, navigator.mediaDevices.getUserMedia({ audio: true })];\n                case 2:\n                    stream = _a.sent();\n                    mediaStreamRef.current = stream;\n                    audioContext = new window.AudioContext();\n                    audioContextRef.current = audioContext;\n                    sampleRate_1 = audioContext.sampleRate;\n                    sourceNode = audioContext.createMediaStreamSource(stream);\n                    sourceNodeRef.current = sourceNode;\n                    scriptProcessor = audioContext.createScriptProcessor(bufferSize, 1, 1);\n                    scriptProcessorRef.current = scriptProcessor;\n                    // Initialize silence detector if enabled\n                    if (enableSilenceDetection) {\n                        silenceDetectorRef.current = new _utils_silence_detection__WEBPACK_IMPORTED_MODULE_3__.SilenceDetector(silenceThreshold, silenceDuration, autoStopOnSilence ? stopStreaming : undefined);\n                    }\n                    scriptProcessor.onaudioprocess = function (audioProcessingEvent) {\n                        try {\n                            var inputBuffer = audioProcessingEvent.inputBuffer;\n                            var channelData = inputBuffer.getChannelData(0);\n                            // Process silence detection if enabled\n                            if (enableSilenceDetection && silenceDetectorRef.current) {\n                                silenceDetectorRef.current.processAudioData(channelData);\n                            }\n                            var downsampledBuffer = (0,_utils_downsample_buffer__WEBPACK_IMPORTED_MODULE_1__.downsampleBuffer)(channelData, sampleRate_1, targetSampleRate);\n                            var base64Data = (0,_utils_int16array_to_base64__WEBPACK_IMPORTED_MODULE_2__.int16ArrayToBase64)(downsampledBuffer);\n                            if (onAudioChunked) {\n                                onAudioChunked(base64Data);\n                            }\n                        }\n                        catch (error) {\n                            handleError(error instanceof Error ? error : new Error(String(error)));\n                        }\n                    };\n                    sourceNode.connect(scriptProcessor);\n                    if (includeDestination) {\n                        scriptProcessor.connect(audioContext.destination);\n                    }\n                    setIsStreaming(true);\n                    if (onStartStreaming) {\n                        onStartStreaming();\n                    }\n                    return [3 /*break*/, 4];\n                case 3:\n                    error_1 = _a.sent();\n                    handleError(error_1 instanceof Error ? error_1 : new Error(String(error_1)));\n                    return [3 /*break*/, 4];\n                case 4: return [2 /*return*/];\n            }\n        });\n    }); }, [\n        isStreaming,\n        onStartStreaming,\n        onAudioChunked,\n        onError,\n        targetSampleRate,\n        bufferSize,\n        enableSilenceDetection,\n        silenceThreshold,\n        silenceDuration,\n        autoStopOnSilence,\n        includeDestination,\n        stopStreaming,\n        handleError,\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        return function () {\n            stopStreaming();\n        };\n    }, [stopStreaming]);\n    return {\n        startStreaming: startStreaming,\n        stopStreaming: stopStreaming,\n        isStreaming: isStreaming,\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/voice-stream/dist/hooks/use-voice-stream.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/voice-stream/dist/index.js":
/*!**************************************************!*\
  !*** ../node_modules/voice-stream/dist/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useVoiceStream: () => (/* reexport safe */ _hooks_use_voice_stream__WEBPACK_IMPORTED_MODULE_0__.useVoiceStream)\n/* harmony export */ });\n/* harmony import */ var _hooks_use_voice_stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hooks/use-voice-stream */ \"(app-pages-browser)/../node_modules/voice-stream/dist/hooks/use-voice-stream.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvdm9pY2Utc3RyZWFtL2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEQiLCJzb3VyY2VzIjpbIkU6XFxwcm90b3R5cGUgbWFzIG11amlcXG5vZGVfbW9kdWxlc1xcdm9pY2Utc3RyZWFtXFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyB1c2VWb2ljZVN0cmVhbSB9IGZyb20gXCIuL2hvb2tzL3VzZS12b2ljZS1zdHJlYW1cIjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/voice-stream/dist/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/voice-stream/dist/utils/downsample-buffer.js":
/*!********************************************************************!*\
  !*** ../node_modules/voice-stream/dist/utils/downsample-buffer.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   downsampleBuffer: () => (/* binding */ downsampleBuffer)\n/* harmony export */ });\nvar downsampleBuffer = function (buffer, sampleRate, outSampleRate) {\n    if (outSampleRate === sampleRate) {\n        var result_1 = new Int16Array(buffer.length);\n        for (var i = 0; i < buffer.length; i++) {\n            var s = Math.max(-1, Math.min(1, buffer[i]));\n            result_1[i] = s < 0 ? s * 0x8000 : s * 0x7fff;\n        }\n        return result_1;\n    }\n    var sampleRateRatio = sampleRate / outSampleRate;\n    var newLength = Math.round(buffer.length / sampleRateRatio);\n    var result = new Int16Array(newLength);\n    var offsetResult = 0;\n    var offsetBuffer = 0;\n    while (offsetResult < newLength) {\n        var nextOffsetBuffer = Math.round((offsetResult + 1) * sampleRateRatio);\n        var accum = 0;\n        var count = 0;\n        for (var i = offsetBuffer; i < nextOffsetBuffer && i < buffer.length; i++) {\n            accum += buffer[i];\n            count++;\n        }\n        var avg = accum / count;\n        var s = Math.max(-1, Math.min(1, avg));\n        result[offsetResult] = s < 0 ? s * 0x8000 : s * 0x7fff;\n        offsetResult++;\n        offsetBuffer = nextOffsetBuffer;\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/voice-stream/dist/utils/downsample-buffer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/voice-stream/dist/utils/int16array-to-base64.js":
/*!***********************************************************************!*\
  !*** ../node_modules/voice-stream/dist/utils/int16array-to-base64.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   int16ArrayToBase64: () => (/* binding */ int16ArrayToBase64)\n/* harmony export */ });\nvar int16ArrayToBase64 = function (buffer) {\n    var binary = \"\";\n    var bytes = new Uint8Array(buffer.buffer);\n    for (var i = 0; i < bytes.byteLength; i++) {\n        binary += String.fromCharCode(bytes[i]);\n    }\n    return btoa(binary);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvdm9pY2Utc3RyZWFtL2Rpc3QvdXRpbHMvaW50MTZhcnJheS10by1iYXNlNjQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBLG9CQUFvQixzQkFBc0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkU6XFxwcm90b3R5cGUgbWFzIG11amlcXG5vZGVfbW9kdWxlc1xcdm9pY2Utc3RyZWFtXFxkaXN0XFx1dGlsc1xcaW50MTZhcnJheS10by1iYXNlNjQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBpbnQxNkFycmF5VG9CYXNlNjQgPSBmdW5jdGlvbiAoYnVmZmVyKSB7XG4gICAgdmFyIGJpbmFyeSA9IFwiXCI7XG4gICAgdmFyIGJ5dGVzID0gbmV3IFVpbnQ4QXJyYXkoYnVmZmVyLmJ1ZmZlcik7XG4gICAgZm9yICh2YXIgaSA9IDA7IGkgPCBieXRlcy5ieXRlTGVuZ3RoOyBpKyspIHtcbiAgICAgICAgYmluYXJ5ICs9IFN0cmluZy5mcm9tQ2hhckNvZGUoYnl0ZXNbaV0pO1xuICAgIH1cbiAgICByZXR1cm4gYnRvYShiaW5hcnkpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/voice-stream/dist/utils/int16array-to-base64.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/voice-stream/dist/utils/silence-detection.js":
/*!********************************************************************!*\
  !*** ../node_modules/voice-stream/dist/utils/silence-detection.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SilenceDetector: () => (/* binding */ SilenceDetector)\n/* harmony export */ });\nvar SilenceDetector = /** @class */ (function () {\n    function SilenceDetector(threshold, duration, onSilenceDetected) {\n        this.silenceStartTime = null;\n        this.threshold = threshold;\n        this.duration = duration;\n        this.onSilenceDetected = onSilenceDetected;\n    }\n    SilenceDetector.prototype.processAudioData = function (channelData) {\n        // Calculate RMS value\n        var sum = 0;\n        for (var i = 0; i < channelData.length; i++) {\n            sum += channelData[i] * channelData[i];\n        }\n        var rms = Math.sqrt(sum / channelData.length);\n        var db = 20 * Math.log10(rms);\n        // Check if audio is below threshold\n        if (db < this.threshold) {\n            if (this.silenceStartTime === null) {\n                this.silenceStartTime = Date.now();\n            }\n            else if (Date.now() - this.silenceStartTime >= this.duration &&\n                this.onSilenceDetected) {\n                this.onSilenceDetected();\n                return true;\n            }\n        }\n        else {\n            this.silenceStartTime = null;\n        }\n        return false;\n    };\n    SilenceDetector.prototype.reset = function () {\n        this.silenceStartTime = null;\n    };\n    return SilenceDetector;\n}());\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/voice-stream/dist/utils/silence-detection.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiRTpcXHByb3RvdHlwZSBtYXMgbXVqaVxccHJvdG90eXBlXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGFwaVxcbmF2aWdhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuLi9jbGllbnQvY29tcG9uZW50cy9uYXZpZ2F0aW9uJztcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprototype%20mas%20muji%5C%5Cprototype%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5C%5BagentId%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprototype%20mas%20muji%5C%5Cprototype%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5C%5BagentId%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/[agentId]/page.tsx */ \"(app-pages-browser)/./src/app/chat/[agentId]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q3Byb3RvdHlwZSUyMG1hcyUyMG11amklNUMlNUNwcm90b3R5cGUlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNjaGF0JTVDJTVDJTVCYWdlbnRJZCU1RCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsNExBQTJHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxwcm90b3R5cGUgbWFzIG11amlcXFxccHJvdG90eXBlXFxcXHNyY1xcXFxhcHBcXFxcY2hhdFxcXFxbYWdlbnRJZF1cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprototype%20mas%20muji%5C%5Cprototype%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5C%5BagentId%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkU6XFxwcm90b3R5cGUgbWFzIG11amlcXHByb3RvdHlwZVxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/chat/[agentId]/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/chat/[agentId]/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VoiceChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useAgentConversation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAgentConversation */ \"(app-pages-browser)/./src/hooks/useAgentConversation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction VoiceChatPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const agentId = params.agentId;\n    const [agentName, setAgentName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('AI Agent');\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { startConversation, stopConversation, isConnected, isListening, userTranscript, agentResponse } = (0,_hooks_useAgentConversation__WEBPACK_IMPORTED_MODULE_3__.useAgentConversation)(agentId);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VoiceChatPage.useEffect\": ()=>{\n            // Fetch agent info to get the name\n            fetchAgentInfo();\n        }\n    }[\"VoiceChatPage.useEffect\"], [\n        agentId\n    ]);\n    const fetchAgentInfo = async ()=>{\n        try {\n            const response = await fetch('/api/agents');\n            if (response.ok) {\n                const data = await response.json();\n                const agent = data.agents.find((a)=>a.agentId === agentId);\n                if (agent) {\n                    setAgentName(agent.name);\n                }\n            }\n        } catch (error) {\n            console.error('Error fetching agent info:', error);\n        }\n    };\n    const handleStartConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VoiceChatPage.useCallback[handleStartConversation]\": async ()=>{\n            try {\n                setError(null);\n                await startConversation();\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                setError('Failed to start conversation. Please check your microphone permissions.');\n            }\n        }\n    }[\"VoiceChatPage.useCallback[handleStartConversation]\"], [\n        startConversation\n    ]);\n    const handleStopConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VoiceChatPage.useCallback[handleStopConversation]\": async ()=>{\n            try {\n                await stopConversation();\n            } catch (error) {\n                console.error('Failed to stop conversation:', error);\n            }\n        }\n    }[\"VoiceChatPage.useCallback[handleStopConversation]\"], [\n        stopConversation\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/'),\n                                    className: \"text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200\",\n                                    children: \"← Back\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-gray-800 dark:text-white\",\n                                    children: [\n                                        \"Voice Chat with \",\n                                        agentName\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                    children: isConnected ? 'Connected' : 'Disconnected'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col items-center justify-center p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl w-full text-center space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-32 h-32 mx-auto rounded-full border-4 transition-all duration-300 \".concat(isListening ? 'border-green-500 bg-green-100 dark:bg-green-900 animate-pulse' : 'border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-800'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-12 h-12 \".concat(isListening ? 'text-green-600' : 'text-gray-400'),\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                isListening && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 w-32 h-32 mx-auto rounded-full border-4 border-green-300 animate-ping\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-800 dark:text-white\",\n                                    children: isConnected ? isListening ? 'Listening...' : 'Ready to chat' : 'Not connected'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-4\",\n                            children: !isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStartConversation,\n                                className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-8 rounded-full text-lg transition-colors duration-200 flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Start Voice Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStopConversation,\n                                className: \"bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-8 rounded-full text-lg transition-colors duration-200 flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Stop Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        (userTranscript || agentResponse) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg space-y-4\",\n                            children: [\n                                userTranscript && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: \"You said:\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-800 dark:text-white bg-blue-50 dark:bg-blue-900 p-3 rounded\",\n                                            children: userTranscript\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 17\n                                }, this),\n                                agentResponse && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: [\n                                                agentName,\n                                                \" responded:\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-800 dark:text-white bg-green-50 dark:bg-green-900 p-3 rounded\",\n                                            children: agentResponse\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 dark:text-gray-400 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        '\\uD83C\\uDFA4 Click \"Start Voice Chat\" to begin talking with ',\n                                        agentName\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"\\uD83D\\uDD0A Make sure your microphone and speakers are working\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"\\uD83D\\uDCAC Speak naturally - the AI will respond with voice\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(VoiceChatPage, \"IoLXzTOw95+YV4KZUxacimscDpE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useAgentConversation__WEBPACK_IMPORTED_MODULE_3__.useAgentConversation\n    ];\n});\n_c = VoiceChatPage;\nvar _c;\n$RefreshReg$(_c, \"VoiceChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/[agentId]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useAgentConversation.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useAgentConversation.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAgentConversation: () => (/* binding */ useAgentConversation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var voice_stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! voice-stream */ \"(app-pages-browser)/../node_modules/voice-stream/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAgentConversation auto */ \n\nconst sendMessage = (websocket, request)=>{\n    if (websocket.readyState !== WebSocket.OPEN) {\n        return;\n    }\n    websocket.send(JSON.stringify(request));\n};\nconst useAgentConversation = (agentId)=>{\n    const websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [userTranscript, setUserTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [agentResponse, setAgentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isPlayingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const audioBufferRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)('');\n    const bufferTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { startStreaming, stopStreaming } = (0,voice_stream__WEBPACK_IMPORTED_MODULE_1__.useVoiceStream)({\n        onAudioChunked: {\n            \"useAgentConversation.useVoiceStream\": (audioData)=>{\n                if (!websocketRef.current) return;\n                sendMessage(websocketRef.current, {\n                    user_audio_chunk: audioData\n                });\n            }\n        }[\"useAgentConversation.useVoiceStream\"]\n    });\n    // Initialize audio context\n    const initAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[initAudioContext]\": async ()=>{\n            if (!audioContextRef.current) {\n                try {\n                    const AudioContextClass = window.AudioContext || window.webkitAudioContext;\n                    audioContextRef.current = new AudioContextClass();\n                    // Resume audio context if it's suspended (required by some browsers)\n                    if (audioContextRef.current.state === 'suspended') {\n                        await audioContextRef.current.resume();\n                    }\n                } catch (error) {\n                    console.error('Failed to initialize audio context:', error);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[initAudioContext]\"], []);\n    // Play audio from base64 using Web Audio API\n    const playAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[playAudio]\": async (base64Audio)=>{\n            try {\n                console.log('Attempting to play audio chunk, length:', base64Audio.length);\n                // Initialize audio context if needed\n                if (!audioContextRef.current) {\n                    await initAudioContext();\n                }\n                if (!audioContextRef.current) {\n                    throw new Error('Audio context not available');\n                }\n                // Method 1: Try Web Audio API with proper decoding\n                try {\n                    // Convert base64 to ArrayBuffer\n                    const binaryString = atob(base64Audio);\n                    const arrayBuffer = new ArrayBuffer(binaryString.length);\n                    const uint8Array = new Uint8Array(arrayBuffer);\n                    for(let i = 0; i < binaryString.length; i++){\n                        uint8Array[i] = binaryString.charCodeAt(i);\n                    }\n                    console.log('Decoded audio buffer size:', arrayBuffer.byteLength);\n                    // Decode audio data\n                    const audioBuffer = await audioContextRef.current.decodeAudioData(arrayBuffer);\n                    console.log('Audio buffer decoded successfully:', audioBuffer.duration, 'seconds');\n                    // Create and play audio source\n                    const source = audioContextRef.current.createBufferSource();\n                    source.buffer = audioBuffer;\n                    source.connect(audioContextRef.current.destination);\n                    return new Promise({\n                        \"useAgentConversation.useCallback[playAudio]\": (resolve)=>{\n                            source.onended = ({\n                                \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                    console.log('Audio playback completed');\n                                    resolve();\n                                }\n                            })[\"useAgentConversation.useCallback[playAudio]\"];\n                            source.start();\n                        }\n                    }[\"useAgentConversation.useCallback[playAudio]\"]);\n                } catch (webAudioError) {\n                    console.log('Web Audio API failed, trying Blob approach:', webAudioError);\n                    // Method 2: Try Blob with different MIME types\n                    const mimeTypes = [\n                        'audio/mpeg',\n                        'audio/mp3',\n                        'audio/wav',\n                        'audio/ogg',\n                        'audio/webm',\n                        'audio/mp4',\n                        'audio/aac'\n                    ];\n                    for (const mimeType of mimeTypes){\n                        try {\n                            // Convert base64 to Blob\n                            const binaryString = atob(base64Audio);\n                            const bytes = new Uint8Array(binaryString.length);\n                            for(let i = 0; i < binaryString.length; i++){\n                                bytes[i] = binaryString.charCodeAt(i);\n                            }\n                            const blob = new Blob([\n                                bytes\n                            ], {\n                                type: mimeType\n                            });\n                            const audioUrl = URL.createObjectURL(blob);\n                            const audio = new Audio(audioUrl);\n                            audio.volume = 0.8;\n                            const playPromise = new Promise({\n                                \"useAgentConversation.useCallback[playAudio]\": (resolve, reject)=>{\n                                    let resolved = false;\n                                    const cleanup = {\n                                        \"useAgentConversation.useCallback[playAudio].cleanup\": ()=>{\n                                            URL.revokeObjectURL(audioUrl);\n                                            audio.onended = null;\n                                            audio.onerror = null;\n                                            audio.oncanplaythrough = null;\n                                        }\n                                    }[\"useAgentConversation.useCallback[playAudio].cleanup\"];\n                                    audio.onended = ({\n                                        \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                            if (!resolved) {\n                                                resolved = true;\n                                                cleanup();\n                                                console.log(\"Audio played successfully with MIME type: \".concat(mimeType));\n                                                resolve();\n                                            }\n                                        }\n                                    })[\"useAgentConversation.useCallback[playAudio]\"];\n                                    audio.onerror = ({\n                                        \"useAgentConversation.useCallback[playAudio]\": (error)=>{\n                                            if (!resolved) {\n                                                cleanup();\n                                                reject(error);\n                                            }\n                                        }\n                                    })[\"useAgentConversation.useCallback[playAudio]\"];\n                                    audio.oncanplaythrough = ({\n                                        \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                            console.log(\"Audio ready with MIME type: \".concat(mimeType));\n                                            audio.play().catch(reject);\n                                        }\n                                    })[\"useAgentConversation.useCallback[playAudio]\"];\n                                    // Fallback\n                                    setTimeout({\n                                        \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                            if (!resolved && audio.readyState >= 2) {\n                                                audio.play().catch(reject);\n                                            }\n                                        }\n                                    }[\"useAgentConversation.useCallback[playAudio]\"], 300);\n                                }\n                            }[\"useAgentConversation.useCallback[playAudio]\"]);\n                            await playPromise;\n                            return; // Success\n                        } catch (blobError) {\n                            console.log(\"MIME type \".concat(mimeType, \" failed:\"), blobError);\n                            continue;\n                        }\n                    }\n                    throw new Error('All playback methods failed');\n                }\n            } catch (error) {\n                console.error('Error playing audio:', error);\n                // Log first few characters of base64 for debugging\n                console.log('Base64 sample:', base64Audio.substring(0, 100));\n            }\n        }\n    }[\"useAgentConversation.useCallback[playAudio]\"], [\n        initAudioContext\n    ]);\n    // Process audio queue\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingRef.current || audioQueueRef.current.length === 0) return;\n            isPlayingRef.current = true;\n            const audioData = audioQueueRef.current.shift();\n            if (audioData) {\n                try {\n                    await playAudio(audioData);\n                } catch (error) {\n                    console.error('Error in processAudioQueue:', error);\n                // Continue processing even if one audio chunk fails\n                }\n            }\n            isPlayingRef.current = false;\n            // Process next audio in queue\n            if (audioQueueRef.current.length > 0) {\n                setTimeout({\n                    \"useAgentConversation.useCallback[processAudioQueue]\": ()=>processAudioQueue()\n                }[\"useAgentConversation.useCallback[processAudioQueue]\"], 50); // Small delay between chunks\n            }\n        }\n    }[\"useAgentConversation.useCallback[processAudioQueue]\"], [\n        playAudio\n    ]);\n    // Buffer audio chunks and play when ready\n    const bufferAudioChunk = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[bufferAudioChunk]\": (base64Audio)=>{\n            // Add chunk to buffer\n            audioBufferRef.current += base64Audio;\n            // Clear existing timeout\n            if (bufferTimeoutRef.current) {\n                clearTimeout(bufferTimeoutRef.current);\n            }\n            // Set timeout to play buffered audio\n            bufferTimeoutRef.current = setTimeout({\n                \"useAgentConversation.useCallback[bufferAudioChunk]\": ()=>{\n                    if (audioBufferRef.current.length > 0) {\n                        console.log('Playing buffered audio, total length:', audioBufferRef.current.length);\n                        audioQueueRef.current.push(audioBufferRef.current);\n                        audioBufferRef.current = ''; // Clear buffer\n                        processAudioQueue();\n                    }\n                }\n            }[\"useAgentConversation.useCallback[bufferAudioChunk]\"], 500); // Wait 500ms for more chunks\n        }\n    }[\"useAgentConversation.useCallback[bufferAudioChunk]\"], [\n        processAudioQueue\n    ]);\n    const startConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n            if (isConnected) return;\n            try {\n                // Request microphone permission\n                await navigator.mediaDevices.getUserMedia({\n                    audio: true\n                });\n                // Initialize audio context for playback\n                await initAudioContext();\n                // Get signed URL for WebSocket connection\n                const response = await fetch(\"/api/conversation/signed-url?agentId=\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to get signed URL');\n                }\n                const { signed_url } = await response.json();\n                const websocket = new WebSocket(signed_url);\n                websocket.onopen = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        sendMessage(websocket, {\n                            type: \"conversation_initiation_client_data\"\n                        });\n                        await startStreaming();\n                        setIsListening(true);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onmessage = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async (event)=>{\n                        const data = JSON.parse(event.data);\n                        // Handle ping events to keep connection alive\n                        if (data.type === \"ping\") {\n                            setTimeout({\n                                \"useAgentConversation.useCallback[startConversation]\": ()=>{\n                                    sendMessage(websocket, {\n                                        type: \"pong\",\n                                        event_id: data.ping_event.event_id\n                                    });\n                                }\n                            }[\"useAgentConversation.useCallback[startConversation]\"], data.ping_event.ping_ms || 0);\n                        }\n                        if (data.type === \"user_transcript\") {\n                            const { user_transcription_event } = data;\n                            setUserTranscript(user_transcription_event.user_transcript);\n                            console.log(\"User transcript:\", user_transcription_event.user_transcript);\n                        }\n                        if (data.type === \"agent_response\") {\n                            const { agent_response_event } = data;\n                            setAgentResponse(agent_response_event.agent_response);\n                            console.log(\"Agent response:\", agent_response_event.agent_response);\n                        }\n                        if (data.type === \"interruption\") {\n                            console.log(\"Conversation interrupted\");\n                            // Clear audio queue on interruption\n                            audioQueueRef.current = [];\n                            isPlayingRef.current = false;\n                        }\n                        if (data.type === \"audio\") {\n                            const { audio_event } = data;\n                            console.log('Received audio chunk, length:', audio_event.audio_base_64.length);\n                            // Debug: Check audio format by looking at base64 header\n                            const base64Sample = audio_event.audio_base_64.substring(0, 50);\n                            console.log('Audio base64 sample:', base64Sample);\n                            // Try to detect format from base64 header\n                            try {\n                                const binaryString = atob(audio_event.audio_base_64.substring(0, 20));\n                                const header = Array.from(binaryString).map({\n                                    \"useAgentConversation.useCallback[startConversation].header\": (c)=>c.charCodeAt(0).toString(16).padStart(2, '0')\n                                }[\"useAgentConversation.useCallback[startConversation].header\"]).join(' ');\n                                console.log('Audio header bytes:', header);\n                            } catch (e) {\n                                console.log('Could not decode header');\n                            }\n                            // Buffer audio chunks instead of playing immediately\n                            bufferAudioChunk(audio_event.audio_base_64);\n                        }\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocketRef.current = websocket;\n                websocket.onclose = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket disconnected');\n                        websocketRef.current = null;\n                        setIsConnected(false);\n                        setIsListening(false);\n                        stopStreaming();\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onerror = ({\n                    \"useAgentConversation.useCallback[startConversation]\": (error)=>{\n                        console.error('WebSocket error:', error);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                throw error;\n            }\n        }\n    }[\"useAgentConversation.useCallback[startConversation]\"], [\n        agentId,\n        isConnected,\n        startStreaming,\n        initAudioContext,\n        bufferAudioChunk\n    ]);\n    const stopConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[stopConversation]\": async ()=>{\n            if (!websocketRef.current) return;\n            websocketRef.current.close();\n            setUserTranscript('');\n            setAgentResponse('');\n            audioQueueRef.current = [];\n            audioBufferRef.current = '';\n            isPlayingRef.current = false;\n            // Clear buffer timeout\n            if (bufferTimeoutRef.current) {\n                clearTimeout(bufferTimeoutRef.current);\n                bufferTimeoutRef.current = null;\n            }\n        }\n    }[\"useAgentConversation.useCallback[stopConversation]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAgentConversation.useEffect\": ()=>{\n            return ({\n                \"useAgentConversation.useEffect\": ()=>{\n                    if (websocketRef.current) {\n                        websocketRef.current.close();\n                    }\n                }\n            })[\"useAgentConversation.useEffect\"];\n        }\n    }[\"useAgentConversation.useEffect\"], []);\n    return {\n        startConversation,\n        stopConversation,\n        isConnected,\n        isListening,\n        userTranscript,\n        agentResponse\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAgentConversation.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cprototype%20mas%20muji%5C%5Cprototype%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5C%5BagentId%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);
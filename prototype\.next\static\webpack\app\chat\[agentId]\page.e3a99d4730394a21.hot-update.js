"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[agentId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useAgentConversation.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useAgentConversation.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAgentConversation: () => (/* binding */ useAgentConversation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var voice_stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! voice-stream */ \"(app-pages-browser)/../node_modules/voice-stream/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAgentConversation auto */ \n\nconst sendMessage = (websocket, request)=>{\n    if (websocket.readyState !== WebSocket.OPEN) {\n        return;\n    }\n    websocket.send(JSON.stringify(request));\n};\nconst useAgentConversation = (agentId)=>{\n    const websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [userTranscript, setUserTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [agentResponse, setAgentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isPlayingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const audioBufferRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const bufferTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { startStreaming, stopStreaming } = (0,voice_stream__WEBPACK_IMPORTED_MODULE_1__.useVoiceStream)({\n        onAudioChunked: {\n            \"useAgentConversation.useVoiceStream\": (audioData)=>{\n                if (!websocketRef.current) return;\n                sendMessage(websocketRef.current, {\n                    user_audio_chunk: audioData\n                });\n            }\n        }[\"useAgentConversation.useVoiceStream\"]\n    });\n    // Initialize audio context\n    const initAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[initAudioContext]\": async ()=>{\n            if (!audioContextRef.current) {\n                try {\n                    const AudioContextClass = window.AudioContext || window.webkitAudioContext;\n                    audioContextRef.current = new AudioContextClass();\n                    // Resume audio context if it's suspended (required by some browsers)\n                    if (audioContextRef.current.state === 'suspended') {\n                        await audioContextRef.current.resume();\n                    }\n                } catch (error) {\n                    console.error('Failed to initialize audio context:', error);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[initAudioContext]\"], []);\n    // Play raw PCM audio from base64 using Web Audio API\n    const playAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[playAudio]\": async (base64Audio)=>{\n            try {\n                console.log('Attempting to play audio chunk, length:', base64Audio.length);\n                // Initialize audio context if needed\n                if (!audioContextRef.current) {\n                    await initAudioContext();\n                }\n                if (!audioContextRef.current) {\n                    throw new Error('Audio context not available');\n                }\n                // Convert base64 to ArrayBuffer\n                const binaryString = atob(base64Audio);\n                const arrayBuffer = new ArrayBuffer(binaryString.length);\n                const uint8Array = new Uint8Array(arrayBuffer);\n                for(let i = 0; i < binaryString.length; i++){\n                    uint8Array[i] = binaryString.charCodeAt(i);\n                }\n                console.log('Decoded audio buffer size:', arrayBuffer.byteLength);\n                // ElevenLabs likely sends raw PCM data, so we need to create AudioBuffer manually\n                try {\n                    // Assume 16-bit PCM, 22050 Hz sample rate (common for ElevenLabs)\n                    const sampleRate = 22050;\n                    const channels = 1; // mono\n                    const bytesPerSample = 2; // 16-bit\n                    const numSamples = arrayBuffer.byteLength / bytesPerSample;\n                    // Create AudioBuffer\n                    const audioBuffer = audioContextRef.current.createBuffer(channels, numSamples, sampleRate);\n                    const channelData = audioBuffer.getChannelData(0);\n                    // Convert 16-bit PCM to float32 (-1 to 1)\n                    const dataView = new DataView(arrayBuffer);\n                    for(let i = 0; i < numSamples; i++){\n                        const sample = dataView.getInt16(i * 2, true); // little-endian\n                        channelData[i] = sample / 32768.0; // Convert to -1 to 1 range\n                    }\n                    console.log('Created PCM audio buffer:', audioBuffer.duration, 'seconds');\n                    // Play the audio\n                    const source = audioContextRef.current.createBufferSource();\n                    source.buffer = audioBuffer;\n                    source.connect(audioContextRef.current.destination);\n                    return new Promise({\n                        \"useAgentConversation.useCallback[playAudio]\": (resolve)=>{\n                            source.onended = ({\n                                \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                    console.log('PCM audio playback completed');\n                                    resolve();\n                                }\n                            })[\"useAgentConversation.useCallback[playAudio]\"];\n                            source.start();\n                        }\n                    }[\"useAgentConversation.useCallback[playAudio]\"]);\n                } catch (pcmError) {\n                    console.log('PCM playback failed, trying different sample rates:', pcmError);\n                    // Try different sample rates\n                    const sampleRates = [\n                        24000,\n                        16000,\n                        44100,\n                        48000\n                    ];\n                    for (const sampleRate of sampleRates){\n                        try {\n                            const channels = 1;\n                            const bytesPerSample = 2;\n                            const numSamples = arrayBuffer.byteLength / bytesPerSample;\n                            const audioBuffer = audioContextRef.current.createBuffer(channels, numSamples, sampleRate);\n                            const channelData = audioBuffer.getChannelData(0);\n                            const dataView = new DataView(arrayBuffer);\n                            for(let i = 0; i < numSamples; i++){\n                                const sample = dataView.getInt16(i * 2, true);\n                                channelData[i] = sample / 32768.0;\n                            }\n                            const source = audioContextRef.current.createBufferSource();\n                            source.buffer = audioBuffer;\n                            source.connect(audioContextRef.current.destination);\n                            console.log(\"Playing with sample rate: \".concat(sampleRate, \"Hz\"));\n                            return new Promise({\n                                \"useAgentConversation.useCallback[playAudio]\": (resolve)=>{\n                                    source.onended = ({\n                                        \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                            console.log(\"Audio played successfully at \".concat(sampleRate, \"Hz\"));\n                                            resolve();\n                                        }\n                                    })[\"useAgentConversation.useCallback[playAudio]\"];\n                                    source.start();\n                                }\n                            }[\"useAgentConversation.useCallback[playAudio]\"]);\n                        } catch (rateError) {\n                            console.log(\"Sample rate \".concat(sampleRate, \"Hz failed, trying next...\"));\n                            continue;\n                        }\n                    }\n                    throw new Error('All sample rates failed');\n                }\n            } catch (error) {\n                console.error('Error playing audio:', error);\n                console.log('Base64 sample:', base64Audio.substring(0, 50));\n            }\n        }\n    }[\"useAgentConversation.useCallback[playAudio]\"], [\n        initAudioContext\n    ]);\n    // Process audio queue\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingRef.current || audioQueueRef.current.length === 0) return;\n            isPlayingRef.current = true;\n            const audioData = audioQueueRef.current.shift();\n            if (audioData) {\n                try {\n                    await playAudio(audioData);\n                } catch (error) {\n                    console.error('Error in processAudioQueue:', error);\n                // Continue processing even if one audio chunk fails\n                }\n            }\n            isPlayingRef.current = false;\n            // Process next audio in queue\n            if (audioQueueRef.current.length > 0) {\n                setTimeout({\n                    \"useAgentConversation.useCallback[processAudioQueue]\": ()=>processAudioQueue()\n                }[\"useAgentConversation.useCallback[processAudioQueue]\"], 50); // Small delay between chunks\n            }\n        }\n    }[\"useAgentConversation.useCallback[processAudioQueue]\"], [\n        playAudio\n    ]);\n    // Play audio chunks directly without complex buffering\n    const handleAudioChunk = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[handleAudioChunk]\": (base64Audio)=>{\n            console.log('Adding audio chunk to queue, length:', base64Audio.length);\n            // Add to queue for sequential playback\n            audioQueueRef.current.push(base64Audio);\n            // Start processing queue if not already playing\n            if (!isPlayingRef.current) {\n                processAudioQueue();\n            }\n        }\n    }[\"useAgentConversation.useCallback[handleAudioChunk]\"], [\n        processAudioQueue\n    ]);\n    const startConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n            if (isConnected) return;\n            try {\n                // Request microphone permission\n                await navigator.mediaDevices.getUserMedia({\n                    audio: true\n                });\n                // Initialize audio context for playback\n                await initAudioContext();\n                // Get signed URL for WebSocket connection\n                const response = await fetch(\"/api/conversation/signed-url?agentId=\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to get signed URL');\n                }\n                const { signed_url } = await response.json();\n                const websocket = new WebSocket(signed_url);\n                websocket.onopen = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        sendMessage(websocket, {\n                            type: \"conversation_initiation_client_data\"\n                        });\n                        await startStreaming();\n                        setIsListening(true);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onmessage = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async (event)=>{\n                        const data = JSON.parse(event.data);\n                        // Handle ping events to keep connection alive\n                        if (data.type === \"ping\") {\n                            setTimeout({\n                                \"useAgentConversation.useCallback[startConversation]\": ()=>{\n                                    sendMessage(websocket, {\n                                        type: \"pong\",\n                                        event_id: data.ping_event.event_id\n                                    });\n                                }\n                            }[\"useAgentConversation.useCallback[startConversation]\"], data.ping_event.ping_ms || 0);\n                        }\n                        if (data.type === \"user_transcript\") {\n                            const { user_transcription_event } = data;\n                            setUserTranscript(user_transcription_event.user_transcript);\n                            console.log(\"User transcript:\", user_transcription_event.user_transcript);\n                        }\n                        if (data.type === \"agent_response\") {\n                            const { agent_response_event } = data;\n                            setAgentResponse(agent_response_event.agent_response);\n                            console.log(\"Agent response:\", agent_response_event.agent_response);\n                        }\n                        if (data.type === \"interruption\") {\n                            console.log(\"Conversation interrupted\");\n                            // Clear audio queue on interruption\n                            audioQueueRef.current = [];\n                            isPlayingRef.current = false;\n                        }\n                        if (data.type === \"audio\") {\n                            const { audio_event } = data;\n                            console.log('Received audio chunk, length:', audio_event.audio_base_64.length);\n                            // Debug: Check audio format by looking at base64 header\n                            const base64Sample = audio_event.audio_base_64.substring(0, 50);\n                            console.log('Audio base64 sample:', base64Sample);\n                            // Try to detect format from base64 header\n                            try {\n                                const binaryString = atob(audio_event.audio_base_64.substring(0, 20));\n                                const header = Array.from(binaryString).map({\n                                    \"useAgentConversation.useCallback[startConversation].header\": (c)=>c.charCodeAt(0).toString(16).padStart(2, '0')\n                                }[\"useAgentConversation.useCallback[startConversation].header\"]).join(' ');\n                                console.log('Audio header bytes:', header);\n                            } catch (e) {\n                                console.log('Could not decode header');\n                            }\n                            // Handle audio chunk directly\n                            handleAudioChunk(audio_event.audio_base_64);\n                        }\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocketRef.current = websocket;\n                websocket.onclose = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket disconnected');\n                        websocketRef.current = null;\n                        setIsConnected(false);\n                        setIsListening(false);\n                        stopStreaming();\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onerror = ({\n                    \"useAgentConversation.useCallback[startConversation]\": (error)=>{\n                        console.error('WebSocket error:', error);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                throw error;\n            }\n        }\n    }[\"useAgentConversation.useCallback[startConversation]\"], [\n        agentId,\n        isConnected,\n        startStreaming,\n        initAudioContext,\n        handleAudioChunk\n    ]);\n    const stopConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[stopConversation]\": async ()=>{\n            if (!websocketRef.current) return;\n            websocketRef.current.close();\n            setUserTranscript('');\n            setAgentResponse('');\n            audioQueueRef.current = [];\n            audioBufferRef.current = [];\n            isPlayingRef.current = false;\n            // Clear buffer timeout\n            if (bufferTimeoutRef.current) {\n                clearTimeout(bufferTimeoutRef.current);\n                bufferTimeoutRef.current = null;\n            }\n        }\n    }[\"useAgentConversation.useCallback[stopConversation]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAgentConversation.useEffect\": ()=>{\n            return ({\n                \"useAgentConversation.useEffect\": ()=>{\n                    if (websocketRef.current) {\n                        websocketRef.current.close();\n                    }\n                }\n            })[\"useAgentConversation.useEffect\"];\n        }\n    }[\"useAgentConversation.useEffect\"], []);\n    return {\n        startConversation,\n        stopConversation,\n        isConnected,\n        isListening,\n        userTranscript,\n        agentResponse\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAgentConversation.ts\n"));

/***/ })

});
/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { VoicePreviewResponseModel } from "./VoicePreviewResponseModel";
export declare const VoiceDesignPreviewResponse: core.serialization.ObjectSchema<serializers.VoiceDesignPreviewResponse.Raw, ElevenLabs.VoiceDesignPreviewResponse>;
export declare namespace VoiceDesignPreviewResponse {
    interface Raw {
        previews: VoicePreviewResponseModel.Raw[];
        text: string;
    }
}

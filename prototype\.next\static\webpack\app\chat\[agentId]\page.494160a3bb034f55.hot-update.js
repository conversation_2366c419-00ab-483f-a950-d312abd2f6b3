"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[agentId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useAgentConversation.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useAgentConversation.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAgentConversation: () => (/* binding */ useAgentConversation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var voice_stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! voice-stream */ \"(app-pages-browser)/../node_modules/voice-stream/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAgentConversation auto */ \n\nconst sendMessage = (websocket, request)=>{\n    if (websocket.readyState !== WebSocket.OPEN) {\n        return;\n    }\n    websocket.send(JSON.stringify(request));\n};\nconst useAgentConversation = (agentId)=>{\n    const websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [userTranscript, setUserTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [agentResponse, setAgentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isPlayingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const audioBufferRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)('');\n    const bufferTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { startStreaming, stopStreaming } = (0,voice_stream__WEBPACK_IMPORTED_MODULE_1__.useVoiceStream)({\n        onAudioChunked: {\n            \"useAgentConversation.useVoiceStream\": (audioData)=>{\n                if (!websocketRef.current) return;\n                sendMessage(websocketRef.current, {\n                    user_audio_chunk: audioData\n                });\n            }\n        }[\"useAgentConversation.useVoiceStream\"]\n    });\n    // Initialize audio context\n    const initAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[initAudioContext]\": async ()=>{\n            if (!audioContextRef.current) {\n                try {\n                    const AudioContextClass = window.AudioContext || window.webkitAudioContext;\n                    audioContextRef.current = new AudioContextClass();\n                    // Resume audio context if it's suspended (required by some browsers)\n                    if (audioContextRef.current.state === 'suspended') {\n                        await audioContextRef.current.resume();\n                    }\n                } catch (error) {\n                    console.error('Failed to initialize audio context:', error);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[initAudioContext]\"], []);\n    // Play audio from base64 using Web Audio API\n    const playAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[playAudio]\": async (base64Audio)=>{\n            try {\n                console.log('Attempting to play audio chunk, length:', base64Audio.length);\n                // Initialize audio context if needed\n                if (!audioContextRef.current) {\n                    await initAudioContext();\n                }\n                if (!audioContextRef.current) {\n                    throw new Error('Audio context not available');\n                }\n                // Method 1: Try Web Audio API with proper decoding\n                try {\n                    // Convert base64 to ArrayBuffer\n                    const binaryString = atob(base64Audio);\n                    const arrayBuffer = new ArrayBuffer(binaryString.length);\n                    const uint8Array = new Uint8Array(arrayBuffer);\n                    for(let i = 0; i < binaryString.length; i++){\n                        uint8Array[i] = binaryString.charCodeAt(i);\n                    }\n                    console.log('Decoded audio buffer size:', arrayBuffer.byteLength);\n                    // Decode audio data\n                    const audioBuffer = await audioContextRef.current.decodeAudioData(arrayBuffer);\n                    console.log('Audio buffer decoded successfully:', audioBuffer.duration, 'seconds');\n                    // Create and play audio source\n                    const source = audioContextRef.current.createBufferSource();\n                    source.buffer = audioBuffer;\n                    source.connect(audioContextRef.current.destination);\n                    return new Promise({\n                        \"useAgentConversation.useCallback[playAudio]\": (resolve)=>{\n                            source.onended = ({\n                                \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                    console.log('Audio playback completed');\n                                    resolve();\n                                }\n                            })[\"useAgentConversation.useCallback[playAudio]\"];\n                            source.start();\n                        }\n                    }[\"useAgentConversation.useCallback[playAudio]\"]);\n                } catch (webAudioError) {\n                    console.log('Web Audio API failed, trying Blob approach:', webAudioError);\n                    // Method 2: Try Blob with different MIME types\n                    const mimeTypes = [\n                        'audio/mpeg',\n                        'audio/mp3',\n                        'audio/wav',\n                        'audio/ogg',\n                        'audio/webm',\n                        'audio/mp4',\n                        'audio/aac'\n                    ];\n                    for (const mimeType of mimeTypes){\n                        try {\n                            // Convert base64 to Blob\n                            const binaryString = atob(base64Audio);\n                            const bytes = new Uint8Array(binaryString.length);\n                            for(let i = 0; i < binaryString.length; i++){\n                                bytes[i] = binaryString.charCodeAt(i);\n                            }\n                            const blob = new Blob([\n                                bytes\n                            ], {\n                                type: mimeType\n                            });\n                            const audioUrl = URL.createObjectURL(blob);\n                            const audio = new Audio(audioUrl);\n                            audio.volume = 0.8;\n                            const playPromise = new Promise({\n                                \"useAgentConversation.useCallback[playAudio]\": (resolve, reject)=>{\n                                    let resolved = false;\n                                    const cleanup = {\n                                        \"useAgentConversation.useCallback[playAudio].cleanup\": ()=>{\n                                            URL.revokeObjectURL(audioUrl);\n                                            audio.onended = null;\n                                            audio.onerror = null;\n                                            audio.oncanplaythrough = null;\n                                        }\n                                    }[\"useAgentConversation.useCallback[playAudio].cleanup\"];\n                                    audio.onended = ({\n                                        \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                            if (!resolved) {\n                                                resolved = true;\n                                                cleanup();\n                                                console.log(\"Audio played successfully with MIME type: \".concat(mimeType));\n                                                resolve();\n                                            }\n                                        }\n                                    })[\"useAgentConversation.useCallback[playAudio]\"];\n                                    audio.onerror = ({\n                                        \"useAgentConversation.useCallback[playAudio]\": (error)=>{\n                                            if (!resolved) {\n                                                cleanup();\n                                                reject(error);\n                                            }\n                                        }\n                                    })[\"useAgentConversation.useCallback[playAudio]\"];\n                                    audio.oncanplaythrough = ({\n                                        \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                            console.log(\"Audio ready with MIME type: \".concat(mimeType));\n                                            audio.play().catch(reject);\n                                        }\n                                    })[\"useAgentConversation.useCallback[playAudio]\"];\n                                    // Fallback\n                                    setTimeout({\n                                        \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                            if (!resolved && audio.readyState >= 2) {\n                                                audio.play().catch(reject);\n                                            }\n                                        }\n                                    }[\"useAgentConversation.useCallback[playAudio]\"], 300);\n                                }\n                            }[\"useAgentConversation.useCallback[playAudio]\"]);\n                            await playPromise;\n                            return; // Success\n                        } catch (blobError) {\n                            console.log(\"MIME type \".concat(mimeType, \" failed:\"), blobError);\n                            continue;\n                        }\n                    }\n                    throw new Error('All playback methods failed');\n                }\n            } catch (error) {\n                console.error('Error playing audio:', error);\n                // Log first few characters of base64 for debugging\n                console.log('Base64 sample:', base64Audio.substring(0, 100));\n            }\n        }\n    }[\"useAgentConversation.useCallback[playAudio]\"], [\n        initAudioContext\n    ]);\n    // Process audio queue\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingRef.current || audioQueueRef.current.length === 0) return;\n            isPlayingRef.current = true;\n            const audioData = audioQueueRef.current.shift();\n            if (audioData) {\n                try {\n                    await playAudio(audioData);\n                } catch (error) {\n                    console.error('Error in processAudioQueue:', error);\n                // Continue processing even if one audio chunk fails\n                }\n            }\n            isPlayingRef.current = false;\n            // Process next audio in queue\n            if (audioQueueRef.current.length > 0) {\n                setTimeout({\n                    \"useAgentConversation.useCallback[processAudioQueue]\": ()=>processAudioQueue()\n                }[\"useAgentConversation.useCallback[processAudioQueue]\"], 50); // Small delay between chunks\n            }\n        }\n    }[\"useAgentConversation.useCallback[processAudioQueue]\"], [\n        playAudio\n    ]);\n    // Buffer audio chunks and play when ready\n    const bufferAudioChunk = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[bufferAudioChunk]\": (base64Audio)=>{\n            // Add chunk to buffer\n            audioBufferRef.current += base64Audio;\n            // Clear existing timeout\n            if (bufferTimeoutRef.current) {\n                clearTimeout(bufferTimeoutRef.current);\n            }\n            // Set timeout to play buffered audio\n            bufferTimeoutRef.current = setTimeout({\n                \"useAgentConversation.useCallback[bufferAudioChunk]\": ()=>{\n                    if (audioBufferRef.current.length > 0) {\n                        console.log('Playing buffered audio, total length:', audioBufferRef.current.length);\n                        audioQueueRef.current.push(audioBufferRef.current);\n                        audioBufferRef.current = ''; // Clear buffer\n                        processAudioQueue();\n                    }\n                }\n            }[\"useAgentConversation.useCallback[bufferAudioChunk]\"], 500); // Wait 500ms for more chunks\n        }\n    }[\"useAgentConversation.useCallback[bufferAudioChunk]\"], [\n        processAudioQueue\n    ]);\n    const startConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n            if (isConnected) return;\n            try {\n                // Request microphone permission\n                await navigator.mediaDevices.getUserMedia({\n                    audio: true\n                });\n                // Get signed URL for WebSocket connection\n                const response = await fetch(\"/api/conversation/signed-url?agentId=\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to get signed URL');\n                }\n                const { signed_url } = await response.json();\n                const websocket = new WebSocket(signed_url);\n                websocket.onopen = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        sendMessage(websocket, {\n                            type: \"conversation_initiation_client_data\"\n                        });\n                        await startStreaming();\n                        setIsListening(true);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onmessage = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async (event)=>{\n                        const data = JSON.parse(event.data);\n                        // Handle ping events to keep connection alive\n                        if (data.type === \"ping\") {\n                            setTimeout({\n                                \"useAgentConversation.useCallback[startConversation]\": ()=>{\n                                    sendMessage(websocket, {\n                                        type: \"pong\",\n                                        event_id: data.ping_event.event_id\n                                    });\n                                }\n                            }[\"useAgentConversation.useCallback[startConversation]\"], data.ping_event.ping_ms || 0);\n                        }\n                        if (data.type === \"user_transcript\") {\n                            const { user_transcription_event } = data;\n                            setUserTranscript(user_transcription_event.user_transcript);\n                            console.log(\"User transcript:\", user_transcription_event.user_transcript);\n                        }\n                        if (data.type === \"agent_response\") {\n                            const { agent_response_event } = data;\n                            setAgentResponse(agent_response_event.agent_response);\n                            console.log(\"Agent response:\", agent_response_event.agent_response);\n                        }\n                        if (data.type === \"interruption\") {\n                            console.log(\"Conversation interrupted\");\n                            // Clear audio queue on interruption\n                            audioQueueRef.current = [];\n                            isPlayingRef.current = false;\n                        }\n                        if (data.type === \"audio\") {\n                            const { audio_event } = data;\n                            console.log('Received audio chunk, length:', audio_event.audio_base_64.length);\n                            // Buffer audio chunks instead of playing immediately\n                            bufferAudioChunk(audio_event.audio_base_64);\n                        }\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocketRef.current = websocket;\n                websocket.onclose = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket disconnected');\n                        websocketRef.current = null;\n                        setIsConnected(false);\n                        setIsListening(false);\n                        stopStreaming();\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onerror = ({\n                    \"useAgentConversation.useCallback[startConversation]\": (error)=>{\n                        console.error('WebSocket error:', error);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                throw error;\n            }\n        }\n    }[\"useAgentConversation.useCallback[startConversation]\"], [\n        agentId,\n        isConnected,\n        startStreaming,\n        initAudioContext,\n        bufferAudioChunk\n    ]);\n    const stopConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[stopConversation]\": async ()=>{\n            if (!websocketRef.current) return;\n            websocketRef.current.close();\n            setUserTranscript('');\n            setAgentResponse('');\n            audioQueueRef.current = [];\n            audioBufferRef.current = '';\n            isPlayingRef.current = false;\n            // Clear buffer timeout\n            if (bufferTimeoutRef.current) {\n                clearTimeout(bufferTimeoutRef.current);\n                bufferTimeoutRef.current = null;\n            }\n        }\n    }[\"useAgentConversation.useCallback[stopConversation]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAgentConversation.useEffect\": ()=>{\n            return ({\n                \"useAgentConversation.useEffect\": ()=>{\n                    if (websocketRef.current) {\n                        websocketRef.current.close();\n                    }\n                }\n            })[\"useAgentConversation.useEffect\"];\n        }\n    }[\"useAgentConversation.useEffect\"], []);\n    return {\n        startConversation,\n        stopConversation,\n        isConnected,\n        isListening,\n        userTranscript,\n        agentResponse\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAgentConversation.ts\n"));

/***/ })

});
/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ConvAiSecretLocator } from "./ConvAiSecretLocator";
import { ConvAiDynamicVariable } from "./ConvAiDynamicVariable";
export declare const WebhookToolApiSchemaConfigInputRequestHeadersValue: core.serialization.Schema<serializers.WebhookToolApiSchemaConfigInputRequestHeadersValue.Raw, ElevenLabs.WebhookToolApiSchemaConfigInputRequestHeadersValue>;
export declare namespace WebhookToolApiSchemaConfigInputRequestHeadersValue {
    type Raw = string | ConvAiSecretLocator.Raw | ConvAiDynamicVariable.Raw;
}

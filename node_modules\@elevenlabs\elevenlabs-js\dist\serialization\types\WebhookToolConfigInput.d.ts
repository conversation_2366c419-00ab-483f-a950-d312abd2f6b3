/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { WebhookToolApiSchemaConfigInput } from "./WebhookToolApiSchemaConfigInput";
import { DynamicVariablesConfig } from "./DynamicVariablesConfig";
export declare const WebhookToolConfigInput: core.serialization.ObjectSchema<serializers.WebhookToolConfigInput.Raw, ElevenLabs.WebhookToolConfigInput>;
export declare namespace WebhookToolConfigInput {
    interface Raw {
        name: string;
        description: string;
        response_timeout_secs?: number | null;
        api_schema: WebhookToolApiSchemaConfigInput.Raw;
        dynamic_variables?: DynamicVariablesConfig.Raw | null;
    }
}

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { UtteranceResponseModel } from "./UtteranceResponseModel";
export declare const SpeakerResponseModel: core.serialization.ObjectSchema<serializers.SpeakerResponseModel.Raw, ElevenLabs.SpeakerResponseModel>;
export declare namespace SpeakerResponseModel {
    interface Raw {
        speaker_id: string;
        duration_secs: number;
        utterances?: UtteranceResponseModel.Raw[] | null;
    }
}

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const WidgetExpandable: core.serialization.Schema<serializers.WidgetExpandable.Raw, ElevenLabs.WidgetExpandable>;
export declare namespace WidgetExpandable {
    type Raw = "never" | "mobile" | "desktop" | "always";
}

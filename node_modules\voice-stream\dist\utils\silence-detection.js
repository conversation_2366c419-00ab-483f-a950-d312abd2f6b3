var SilenceDetector = /** @class */ (function () {
    function SilenceDetector(threshold, duration, onSilenceDetected) {
        this.silenceStartTime = null;
        this.threshold = threshold;
        this.duration = duration;
        this.onSilenceDetected = onSilenceDetected;
    }
    SilenceDetector.prototype.processAudioData = function (channelData) {
        // Calculate RMS value
        var sum = 0;
        for (var i = 0; i < channelData.length; i++) {
            sum += channelData[i] * channelData[i];
        }
        var rms = Math.sqrt(sum / channelData.length);
        var db = 20 * Math.log10(rms);
        // Check if audio is below threshold
        if (db < this.threshold) {
            if (this.silenceStartTime === null) {
                this.silenceStartTime = Date.now();
            }
            else if (Date.now() - this.silenceStartTime >= this.duration &&
                this.onSilenceDetected) {
                this.onSilenceDetected();
                return true;
            }
        }
        else {
            this.silenceStartTime = null;
        }
        return false;
    };
    SilenceDetector.prototype.reset = function () {
        this.silenceStartTime = null;
    };
    return SilenceDetector;
}());
export { SilenceDetector };

/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { VoiceSharingState } from "./VoiceSharingState";
import { VoiceSharingResponseModelCategory } from "./VoiceSharingResponseModelCategory";
import { ReviewStatus } from "./ReviewStatus";
import { VoiceSharingModerationCheckResponseModel } from "./VoiceSharingModerationCheckResponseModel";
import { ReaderResourceResponseModel } from "./ReaderResourceResponseModel";
export declare const VoiceSharingResponse: core.serialization.ObjectSchema<serializers.VoiceSharingResponse.Raw, ElevenLabs.VoiceSharingResponse>;
export declare namespace VoiceSharingResponse {
    interface Raw {
        status?: VoiceSharingState.Raw | null;
        history_item_sample_id?: string | null;
        date_unix?: number | null;
        whitelisted_emails?: string[] | null;
        public_owner_id?: string | null;
        original_voice_id?: string | null;
        financial_rewards_enabled?: boolean | null;
        free_users_allowed?: boolean | null;
        live_moderation_enabled?: boolean | null;
        rate?: number | null;
        fiat_rate?: number | null;
        notice_period?: number | null;
        disable_at_unix?: number | null;
        voice_mixing_allowed?: boolean | null;
        featured?: boolean | null;
        category?: VoiceSharingResponseModelCategory.Raw | null;
        reader_app_enabled?: boolean | null;
        image_url?: string | null;
        ban_reason?: string | null;
        liked_by_count?: number | null;
        cloned_by_count?: number | null;
        name?: string | null;
        description?: string | null;
        labels?: Record<string, string> | null;
        review_status?: ReviewStatus.Raw | null;
        review_message?: string | null;
        enabled_in_library?: boolean | null;
        instagram_username?: string | null;
        twitter_username?: string | null;
        youtube_username?: string | null;
        tiktok_username?: string | null;
        moderation_check?: VoiceSharingModerationCheckResponseModel.Raw | null;
        reader_restricted_on?: ReaderResourceResponseModel.Raw[] | null;
    }
}

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const StartPvcVoiceTrainingResponseModel: core.serialization.ObjectSchema<serializers.StartPvcVoiceTrainingResponseModel.Raw, ElevenLabs.StartPvcVoiceTrainingResponseModel>;
export declare namespace StartPvcVoiceTrainingResponseModel {
    interface Raw {
        status: string;
    }
}

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SendTextMulti: core.serialization.ObjectSchema<serializers.SendTextMulti.Raw, ElevenLabs.SendTextMulti>;
export declare namespace SendTextMulti {
    interface Raw {
        text: string;
        context_id?: string | null;
        flush?: boolean | null;
    }
}

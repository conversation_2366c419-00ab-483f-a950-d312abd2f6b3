/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SipMediaEncryptionEnum: core.serialization.Schema<serializers.SipMediaEncryptionEnum.Raw, ElevenLabs.SipMediaEncryptionEnum>;
export declare namespace SipMediaEncryptionEnum {
    type Raw = "disabled" | "allowed" | "required";
}

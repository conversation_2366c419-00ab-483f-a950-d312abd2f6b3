/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const VoiceResponseModelSafetyControl: core.serialization.Schema<serializers.VoiceResponseModelSafetyControl.Raw, ElevenLabs.VoiceResponseModelSafetyControl>;
export declare namespace VoiceResponseModelSafetyControl {
    type Raw = "NONE" | "BAN" | "CAPTCHA" | "CAPTCHA_AND_MODERATION" | "ENTERPRISE_BAN" | "ENTERPRISE_CAPTCHA";
}

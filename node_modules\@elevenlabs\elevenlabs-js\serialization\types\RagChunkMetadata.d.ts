/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const RagChunkMetadata: core.serialization.ObjectSchema<serializers.RagChunkMetadata.Raw, ElevenLabs.RagChunkMetadata>;
export declare namespace RagChunkMetadata {
    interface Raw {
        document_id: string;
        chunk_id: string;
        vector_distance: number;
    }
}

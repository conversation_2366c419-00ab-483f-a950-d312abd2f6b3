/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const PronunciationDictionaryVersionResponseModelPermissionOnResource: core.serialization.Schema<serializers.PronunciationDictionaryVersionResponseModelPermissionOnResource.Raw, ElevenLabs.PronunciationDictionaryVersionResponseModelPermissionOnResource>;
export declare namespace PronunciationDictionaryVersionResponseModelPermissionOnResource {
    type Raw = "admin" | "editor" | "viewer";
}

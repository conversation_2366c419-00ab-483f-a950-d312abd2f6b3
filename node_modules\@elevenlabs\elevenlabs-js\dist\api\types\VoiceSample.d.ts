/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface VoiceSample {
    /** The ID of the sample. */
    sampleId?: string;
    /** The name of the sample file. */
    fileName?: string;
    /** The MIME type of the sample file. */
    mimeType?: string;
    /** The size of the sample file in bytes. */
    sizeBytes?: number;
    /** The hash of the sample file. */
    hash?: string;
    durationSecs?: number;
    removeBackgroundNoise?: boolean;
    hasIsolatedAudio?: boolean;
    hasIsolatedAudioPreview?: boolean;
    speakerSeparation?: ElevenLabs.SpeakerSeparationResponseModel;
    trimStart?: number;
    trimEnd?: number;
}

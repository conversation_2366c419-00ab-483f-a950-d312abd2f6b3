/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SpeakerUpdatedResponse: core.serialization.ObjectSchema<serializers.SpeakerUpdatedResponse.Raw, ElevenLabs.SpeakerUpdatedResponse>;
export declare namespace SpeakerUpdatedResponse {
    interface Raw {
        version: number;
    }
}

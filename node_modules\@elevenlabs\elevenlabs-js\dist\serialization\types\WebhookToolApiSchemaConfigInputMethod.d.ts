/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const WebhookToolApiSchemaConfigInputMethod: core.serialization.Schema<serializers.WebhookToolApiSchemaConfigInputMethod.Raw, ElevenLabs.WebhookToolApiSchemaConfigInputMethod>;
export declare namespace WebhookToolApiSchemaConfigInputMethod {
    type Raw = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";
}

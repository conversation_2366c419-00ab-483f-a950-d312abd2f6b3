/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const RequestPvcManualVerificationResponseModel: core.serialization.ObjectSchema<serializers.RequestPvcManualVerificationResponseModel.Raw, ElevenLabs.RequestPvcManualVerificationResponseModel>;
export declare namespace RequestPvcManualVerificationResponseModel {
    interface Raw {
        status: string;
    }
}

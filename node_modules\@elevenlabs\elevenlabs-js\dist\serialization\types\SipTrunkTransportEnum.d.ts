/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SipTrunkTransportEnum: core.serialization.Schema<serializers.SipTrunkTransportEnum.Raw, ElevenLabs.SipTrunkTransportEnum>;
export declare namespace SipTrunkTransportEnum {
    type Raw = "auto" | "udp" | "tcp" | "tls";
}

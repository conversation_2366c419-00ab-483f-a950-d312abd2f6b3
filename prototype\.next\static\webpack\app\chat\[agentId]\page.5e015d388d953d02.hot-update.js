"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[agentId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useAgentConversation.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useAgentConversation.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAgentConversation: () => (/* binding */ useAgentConversation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var voice_stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! voice-stream */ \"(app-pages-browser)/../node_modules/voice-stream/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAgentConversation auto */ \n\nconst sendMessage = (websocket, request)=>{\n    if (websocket.readyState !== WebSocket.OPEN) {\n        return;\n    }\n    websocket.send(JSON.stringify(request));\n};\nconst useAgentConversation = (agentId)=>{\n    const websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [userTranscript, setUserTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [agentResponse, setAgentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isPlayingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const audioBufferRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const bufferTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { startStreaming, stopStreaming } = (0,voice_stream__WEBPACK_IMPORTED_MODULE_1__.useVoiceStream)({\n        onAudioChunked: {\n            \"useAgentConversation.useVoiceStream\": (audioData)=>{\n                if (!websocketRef.current) return;\n                sendMessage(websocketRef.current, {\n                    user_audio_chunk: audioData\n                });\n            }\n        }[\"useAgentConversation.useVoiceStream\"]\n    });\n    // Initialize audio context\n    const initAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[initAudioContext]\": async ()=>{\n            if (!audioContextRef.current) {\n                try {\n                    const AudioContextClass = window.AudioContext || window.webkitAudioContext;\n                    audioContextRef.current = new AudioContextClass();\n                    // Resume audio context if it's suspended (required by some browsers)\n                    if (audioContextRef.current.state === 'suspended') {\n                        await audioContextRef.current.resume();\n                    }\n                } catch (error) {\n                    console.error('Failed to initialize audio context:', error);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[initAudioContext]\"], []);\n    // Play audio from base64 - try as MP3 first, then fallback to PCM\n    const playAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[playAudio]\": async (base64Audio)=>{\n            try {\n                console.log('Attempting to play audio chunk, length:', base64Audio.length);\n                // Method 1: Try as MP3 using HTML Audio (ElevenLabs default format)\n                try {\n                    console.log('🎵 Trying as MP3 format...');\n                    const audio = new Audio(\"data:audio/mpeg;base64,\".concat(base64Audio));\n                    audio.volume = 0.8;\n                    return new Promise({\n                        \"useAgentConversation.useCallback[playAudio]\": (resolve, reject)=>{\n                            let resolved = false;\n                            const cleanup = {\n                                \"useAgentConversation.useCallback[playAudio].cleanup\": ()=>{\n                                    audio.onended = null;\n                                    audio.onerror = null;\n                                    audio.oncanplaythrough = null;\n                                }\n                            }[\"useAgentConversation.useCallback[playAudio].cleanup\"];\n                            audio.onended = ({\n                                \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                    if (!resolved) {\n                                        resolved = true;\n                                        cleanup();\n                                        console.log('✅ Audio played successfully as MP3');\n                                        resolve();\n                                    }\n                                }\n                            })[\"useAgentConversation.useCallback[playAudio]\"];\n                            audio.onerror = ({\n                                \"useAgentConversation.useCallback[playAudio]\": (error)=>{\n                                    if (!resolved) {\n                                        cleanup();\n                                        console.log('❌ MP3 playback failed, trying other formats...');\n                                        reject(error);\n                                    }\n                                }\n                            })[\"useAgentConversation.useCallback[playAudio]\"];\n                            audio.oncanplaythrough = ({\n                                \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                    console.log('🎶 MP3 audio ready to play');\n                                    audio.play().catch(reject);\n                                }\n                            })[\"useAgentConversation.useCallback[playAudio]\"];\n                            // Fallback timeout\n                            setTimeout({\n                                \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                    if (!resolved && audio.readyState >= 2) {\n                                        audio.play().catch(reject);\n                                    }\n                                }\n                            }[\"useAgentConversation.useCallback[playAudio]\"], 500);\n                        }\n                    }[\"useAgentConversation.useCallback[playAudio]\"]);\n                } catch (mp3Error) {\n                    console.log('MP3 method failed, trying Web Audio API:', mp3Error);\n                    // Method 2: Try Web Audio API with automatic decoding\n                    if (!audioContextRef.current) {\n                        await initAudioContext();\n                    }\n                    if (!audioContextRef.current) {\n                        throw new Error('Audio context not available');\n                    }\n                    // Convert base64 to ArrayBuffer\n                    const binaryString = atob(base64Audio);\n                    const arrayBuffer = new ArrayBuffer(binaryString.length);\n                    const uint8Array = new Uint8Array(arrayBuffer);\n                    for(let i = 0; i < binaryString.length; i++){\n                        uint8Array[i] = binaryString.charCodeAt(i);\n                    }\n                    console.log('🔊 Trying Web Audio API automatic decoding...');\n                    try {\n                        // Let Web Audio API automatically decode the format\n                        const audioBuffer = await audioContextRef.current.decodeAudioData(arrayBuffer);\n                        console.log(\"✅ Auto-decoded audio: \".concat(audioBuffer.duration.toFixed(2), \"s, \").concat(audioBuffer.sampleRate, \"Hz\"));\n                        const source = audioContextRef.current.createBufferSource();\n                        source.buffer = audioBuffer;\n                        source.connect(audioContextRef.current.destination);\n                        return new Promise({\n                            \"useAgentConversation.useCallback[playAudio]\": (resolve)=>{\n                                source.onended = ({\n                                    \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                        console.log('✅ Audio played successfully via Web Audio API');\n                                        resolve();\n                                    }\n                                })[\"useAgentConversation.useCallback[playAudio]\"];\n                                source.start();\n                            }\n                        }[\"useAgentConversation.useCallback[playAudio]\"]);\n                    } catch (decodeError) {\n                        console.log('❌ Auto-decode failed:', decodeError);\n                        throw new Error('All audio playback methods failed');\n                    }\n                }\n            } catch (error) {\n                console.error('Error playing audio:', error);\n                console.log('Base64 sample:', base64Audio.substring(0, 50));\n            }\n        }\n    }[\"useAgentConversation.useCallback[playAudio]\"], [\n        initAudioContext\n    ]);\n    // Process audio queue\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingRef.current || audioQueueRef.current.length === 0) return;\n            isPlayingRef.current = true;\n            const audioData = audioQueueRef.current.shift();\n            if (audioData) {\n                try {\n                    await playAudio(audioData);\n                } catch (error) {\n                    console.error('Error in processAudioQueue:', error);\n                // Continue processing even if one audio chunk fails\n                }\n            }\n            isPlayingRef.current = false;\n            // Process next audio in queue\n            if (audioQueueRef.current.length > 0) {\n                setTimeout({\n                    \"useAgentConversation.useCallback[processAudioQueue]\": ()=>processAudioQueue()\n                }[\"useAgentConversation.useCallback[processAudioQueue]\"], 50); // Small delay between chunks\n            }\n        }\n    }[\"useAgentConversation.useCallback[processAudioQueue]\"], [\n        playAudio\n    ]);\n    // Play audio chunks directly without complex buffering\n    const handleAudioChunk = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[handleAudioChunk]\": (base64Audio)=>{\n            console.log('Adding audio chunk to queue, length:', base64Audio.length);\n            // Add to queue for sequential playback\n            audioQueueRef.current.push(base64Audio);\n            // Start processing queue if not already playing\n            if (!isPlayingRef.current) {\n                processAudioQueue();\n            }\n        }\n    }[\"useAgentConversation.useCallback[handleAudioChunk]\"], [\n        processAudioQueue\n    ]);\n    const startConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n            if (isConnected) return;\n            try {\n                // Request microphone permission\n                await navigator.mediaDevices.getUserMedia({\n                    audio: true\n                });\n                // Initialize audio context for playback\n                await initAudioContext();\n                // Get signed URL for WebSocket connection\n                const response = await fetch(\"/api/conversation/signed-url?agentId=\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to get signed URL');\n                }\n                const { signed_url } = await response.json();\n                const websocket = new WebSocket(signed_url);\n                websocket.onopen = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        sendMessage(websocket, {\n                            type: \"conversation_initiation_client_data\"\n                        });\n                        await startStreaming();\n                        setIsListening(true);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onmessage = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async (event)=>{\n                        const data = JSON.parse(event.data);\n                        // Handle ping events to keep connection alive\n                        if (data.type === \"ping\") {\n                            setTimeout({\n                                \"useAgentConversation.useCallback[startConversation]\": ()=>{\n                                    sendMessage(websocket, {\n                                        type: \"pong\",\n                                        event_id: data.ping_event.event_id\n                                    });\n                                }\n                            }[\"useAgentConversation.useCallback[startConversation]\"], data.ping_event.ping_ms || 0);\n                        }\n                        if (data.type === \"user_transcript\") {\n                            const { user_transcription_event } = data;\n                            setUserTranscript(user_transcription_event.user_transcript);\n                            console.log(\"User transcript:\", user_transcription_event.user_transcript);\n                        }\n                        if (data.type === \"agent_response\") {\n                            const { agent_response_event } = data;\n                            setAgentResponse(agent_response_event.agent_response);\n                            console.log(\"Agent response:\", agent_response_event.agent_response);\n                        }\n                        if (data.type === \"interruption\") {\n                            console.log(\"Conversation interrupted\");\n                            // Clear audio queue on interruption\n                            audioQueueRef.current = [];\n                            isPlayingRef.current = false;\n                        }\n                        if (data.type === \"audio\") {\n                            const { audio_event } = data;\n                            console.log('Received audio chunk, length:', audio_event.audio_base_64.length);\n                            // Debug: Check audio format by looking at base64 header\n                            const base64Sample = audio_event.audio_base_64.substring(0, 50);\n                            console.log('Audio base64 sample:', base64Sample);\n                            // Try to detect format from base64 header\n                            try {\n                                const binaryString = atob(audio_event.audio_base_64.substring(0, 20));\n                                const header = Array.from(binaryString).map({\n                                    \"useAgentConversation.useCallback[startConversation].header\": (c)=>c.charCodeAt(0).toString(16).padStart(2, '0')\n                                }[\"useAgentConversation.useCallback[startConversation].header\"]).join(' ');\n                                console.log('Audio header bytes:', header);\n                            } catch (e) {\n                                console.log('Could not decode header');\n                            }\n                            // Handle audio chunk directly\n                            handleAudioChunk(audio_event.audio_base_64);\n                        }\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocketRef.current = websocket;\n                websocket.onclose = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket disconnected');\n                        websocketRef.current = null;\n                        setIsConnected(false);\n                        setIsListening(false);\n                        stopStreaming();\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onerror = ({\n                    \"useAgentConversation.useCallback[startConversation]\": (error)=>{\n                        console.error('WebSocket error:', error);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                throw error;\n            }\n        }\n    }[\"useAgentConversation.useCallback[startConversation]\"], [\n        agentId,\n        isConnected,\n        startStreaming,\n        initAudioContext,\n        handleAudioChunk\n    ]);\n    const stopConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[stopConversation]\": async ()=>{\n            if (!websocketRef.current) return;\n            websocketRef.current.close();\n            setUserTranscript('');\n            setAgentResponse('');\n            audioQueueRef.current = [];\n            isPlayingRef.current = false;\n        }\n    }[\"useAgentConversation.useCallback[stopConversation]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAgentConversation.useEffect\": ()=>{\n            return ({\n                \"useAgentConversation.useEffect\": ()=>{\n                    if (websocketRef.current) {\n                        websocketRef.current.close();\n                    }\n                }\n            })[\"useAgentConversation.useEffect\"];\n        }\n    }[\"useAgentConversation.useEffect\"], []);\n    return {\n        startConversation,\n        stopConversation,\n        isConnected,\n        isListening,\n        userTranscript,\n        agentResponse\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAgentConversation.ts\n"));

/***/ })

});
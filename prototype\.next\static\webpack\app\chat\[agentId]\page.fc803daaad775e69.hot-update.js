"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[agentId]/page",{

/***/ "(app-pages-browser)/./src/app/chat/[agentId]/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/chat/[agentId]/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ChatPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const agentId = params.agentId;\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [agentName, setAgentName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('AI Agent');\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatPage.useEffect\"], [\n        messages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            // Fetch agent info to get the name\n            fetchAgentInfo();\n        }\n    }[\"ChatPage.useEffect\"], [\n        agentId\n    ]);\n    const fetchAgentInfo = async ()=>{\n        try {\n            const response = await fetch('/api/agents');\n            if (response.ok) {\n                const data = await response.json();\n                const agent = data.agents.find((a)=>a.agentId === agentId);\n                if (agent) {\n                    setAgentName(agent.name);\n                }\n            }\n        } catch (error) {\n            console.error('Error fetching agent info:', error);\n        }\n    };\n    const sendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = {\n            role: 'user',\n            content: inputMessage,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputMessage('');\n        setIsLoading(true);\n        try {\n            var _response_body;\n            const response = await fetch(\"/api/chat/\".concat(agentId), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: inputMessage,\n                    language: 'en'\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to send message');\n            }\n            const reader = (_response_body = response.body) === null || _response_body === void 0 ? void 0 : _response_body.getReader();\n            if (!reader) {\n                throw new Error('No response stream');\n            }\n            let agentResponse = '';\n            const agentMessage = {\n                role: 'agent',\n                content: '',\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    agentMessage\n                ]);\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) break;\n                const chunk = new TextDecoder().decode(value);\n                const lines = chunk.split('\\n').filter((line)=>line.trim());\n                for (const line of lines){\n                    try {\n                        const data = JSON.parse(line);\n                        // Process the streaming data based on ElevenLabs response format\n                        if (data.simulated_conversation) {\n                            for (const turn of data.simulated_conversation){\n                                if (turn.role === 'agent' || turn.role === 'assistant') {\n                                    agentResponse += turn.message || '';\n                                }\n                            }\n                        }\n                        // Update the agent message in real-time\n                        setMessages((prev)=>prev.map((msg, index)=>index === prev.length - 1 && msg.role === 'agent' ? {\n                                    ...msg,\n                                    content: agentResponse\n                                } : msg));\n                    } catch (parseError) {\n                        console.error('Error parsing stream data:', parseError);\n                    }\n                }\n            }\n        } catch (error) {\n            console.error('Error sending message:', error);\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        role: 'agent',\n                        content: 'Sorry, I encountered an error. Please try again.',\n                        timestamp: new Date()\n                    }\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/'),\n                                className: \"text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200\",\n                                children: \"← Back\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-semibold text-gray-800 dark:text-white\",\n                                children: [\n                                    \"Chat with \",\n                                    agentName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto space-y-4\",\n                    children: [\n                        messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-gray-500 dark:text-gray-400 py-8\",\n                            children: [\n                                \"Start a conversation with \",\n                                agentName\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, this),\n                        messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-xs lg:max-w-md px-4 py-2 rounded-lg \".concat(message.role === 'user' ? 'bg-blue-600 text-white' : 'bg-white dark:bg-gray-800 text-gray-800 dark:text-white border border-gray-200 dark:border-gray-700'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: message.content\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs mt-1 \".concat(message.role === 'user' ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'),\n                                            children: message.timestamp.toLocaleTimeString()\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this)\n                            }, index, false, {\n                                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-4 py-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                            style: {\n                                                animationDelay: '0.1s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                            style: {\n                                                animationDelay: '0.2s'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto flex space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: inputMessage,\n                            onChange: (e)=>setInputMessage(e.target.value),\n                            onKeyPress: handleKeyPress,\n                            placeholder: \"Type your message...\",\n                            className: \"flex-1 resize-none border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\",\n                            rows: 1,\n                            disabled: isLoading\n                        }, void 0, false, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: sendMessage,\n                            disabled: !inputMessage.trim() || isLoading,\n                            className: \"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-2 px-6 rounded-lg transition-colors duration-200\",\n                            children: \"Send\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"yKR89zFEu655EvVpzngJWNvVsUs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/[agentId]/page.tsx\n"));

/***/ })

});
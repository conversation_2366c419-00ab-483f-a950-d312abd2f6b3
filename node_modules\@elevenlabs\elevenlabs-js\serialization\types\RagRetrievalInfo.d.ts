/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { RagChunkMetadata } from "./RagChunkMetadata";
import { EmbeddingModelEnum } from "./EmbeddingModelEnum";
export declare const RagRetrievalInfo: core.serialization.ObjectSchema<serializers.RagRetrievalInfo.Raw, ElevenLabs.RagRetrievalInfo>;
export declare namespace RagRetrievalInfo {
    interface Raw {
        chunks: RagChunkMetadata.Raw[];
        embedding_model: EmbeddingModelEnum.Raw;
        retrieval_query: string;
        rag_latency_secs: number;
    }
}

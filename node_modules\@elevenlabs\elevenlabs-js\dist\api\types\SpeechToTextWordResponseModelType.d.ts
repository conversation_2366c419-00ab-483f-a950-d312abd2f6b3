/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * The type of the word or sound. 'audio_event' is used for non-word sounds like laughter or footsteps.
 */
export type SpeechToTextWordResponseModelType = "word" | "spacing" | "audio_event";
export declare const SpeechToTextWordResponseModelType: {
    readonly Word: "word";
    readonly Spacing: "spacing";
    readonly AudioEvent: "audio_event";
};

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SimilarVoiceCategory: core.serialization.Schema<serializers.SimilarVoiceCategory.Raw, ElevenLabs.SimilarVoiceCategory>;
export declare namespace SimilarVoiceCategory {
    type Raw = "premade" | "cloned" | "generated" | "professional" | "famous";
}

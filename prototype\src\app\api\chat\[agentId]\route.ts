import { NextRequest, NextResponse } from 'next/server';
import { ElevenLabsClient } from '@elevenlabs/elevenlabs-js';

export async function POST(
  request: NextRequest,
  { params }: { params: { agentId: string } }
) {
  try {
    const apiKey = process.env.ELEVENLABS_API_KEY;
    
    if (!apiKey) {
      return NextResponse.json(
        { error: 'ElevenLabs API key not configured' },
        { status: 500 }
      );
    }

    const { message, language = 'en' } = await request.json();
    
    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    const client = new ElevenLabsClient({ apiKey });
    
    // Create a readable stream for the response
    const stream = new ReadableStream({
      async start(controller) {
        try {
          const conversationStream = await client.conversationalAi.agents.simulateConversationStream(
            params.agentId,
            {
              simulationSpecification: {
                simulatedUserConfig: {
                  first_message: message,
                  language: language
                }
              }
            }
          );

          // Process the stream
          for await (const chunk of conversationStream) {
            const data = JSON.stringify(chunk) + '\n';
            controller.enqueue(new TextEncoder().encode(data));
          }
          
          controller.close();
        } catch (error) {
          console.error('Streaming error:', error);
          controller.error(error);
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });
    
  } catch (error) {
    console.error('Error in chat API:', error);
    return NextResponse.json(
      { error: 'Failed to process chat request' },
      { status: 500 }
    );
  }
}

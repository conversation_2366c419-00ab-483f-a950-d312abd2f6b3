"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[agentId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useAgentConversation.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useAgentConversation.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAgentConversation: () => (/* binding */ useAgentConversation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var voice_stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! voice-stream */ \"(app-pages-browser)/../node_modules/voice-stream/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAgentConversation auto */ \n\nconst sendMessage = (websocket, request)=>{\n    if (websocket.readyState !== WebSocket.OPEN) {\n        return;\n    }\n    websocket.send(JSON.stringify(request));\n};\nconst useAgentConversation = (agentId)=>{\n    const websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [userTranscript, setUserTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [agentResponse, setAgentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isPlayingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const audioBufferRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const bufferTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { startStreaming, stopStreaming } = (0,voice_stream__WEBPACK_IMPORTED_MODULE_1__.useVoiceStream)({\n        onAudioChunked: {\n            \"useAgentConversation.useVoiceStream\": (audioData)=>{\n                if (!websocketRef.current) return;\n                sendMessage(websocketRef.current, {\n                    user_audio_chunk: audioData\n                });\n            }\n        }[\"useAgentConversation.useVoiceStream\"]\n    });\n    // Initialize audio context\n    const initAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[initAudioContext]\": async ()=>{\n            if (!audioContextRef.current) {\n                try {\n                    const AudioContextClass = window.AudioContext || window.webkitAudioContext;\n                    audioContextRef.current = new AudioContextClass();\n                    // Resume audio context if it's suspended (required by some browsers)\n                    if (audioContextRef.current.state === 'suspended') {\n                        await audioContextRef.current.resume();\n                    }\n                } catch (error) {\n                    console.error('Failed to initialize audio context:', error);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[initAudioContext]\"], []);\n    // Play raw PCM audio from base64 using Web Audio API\n    const playAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[playAudio]\": async (base64Audio)=>{\n            try {\n                console.log('Attempting to play audio chunk, length:', base64Audio.length);\n                // Initialize audio context if needed\n                if (!audioContextRef.current) {\n                    await initAudioContext();\n                }\n                if (!audioContextRef.current) {\n                    throw new Error('Audio context not available');\n                }\n                // Convert base64 to ArrayBuffer\n                const binaryString = atob(base64Audio);\n                const arrayBuffer = new ArrayBuffer(binaryString.length);\n                const uint8Array = new Uint8Array(arrayBuffer);\n                for(let i = 0; i < binaryString.length; i++){\n                    uint8Array[i] = binaryString.charCodeAt(i);\n                }\n                console.log('Decoded audio buffer size:', arrayBuffer.byteLength);\n                // ElevenLabs likely sends raw PCM data, so we need to create AudioBuffer manually\n                try {\n                    // Assume 16-bit PCM, 22050 Hz sample rate (common for ElevenLabs)\n                    const sampleRate = 22050;\n                    const channels = 1; // mono\n                    const bytesPerSample = 2; // 16-bit\n                    const numSamples = arrayBuffer.byteLength / bytesPerSample;\n                    // Create AudioBuffer\n                    const audioBuffer = audioContextRef.current.createBuffer(channels, numSamples, sampleRate);\n                    const channelData = audioBuffer.getChannelData(0);\n                    // Convert 16-bit PCM to float32 (-1 to 1)\n                    const dataView = new DataView(arrayBuffer);\n                    for(let i = 0; i < numSamples; i++){\n                        const sample = dataView.getInt16(i * 2, true); // little-endian\n                        channelData[i] = sample / 32768.0; // Convert to -1 to 1 range\n                    }\n                    console.log('Created PCM audio buffer:', audioBuffer.duration, 'seconds');\n                    // Play the audio\n                    const source = audioContextRef.current.createBufferSource();\n                    source.buffer = audioBuffer;\n                    source.connect(audioContextRef.current.destination);\n                    return new Promise({\n                        \"useAgentConversation.useCallback[playAudio]\": (resolve)=>{\n                            source.onended = ({\n                                \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                    console.log('PCM audio playback completed');\n                                    resolve();\n                                }\n                            })[\"useAgentConversation.useCallback[playAudio]\"];\n                            source.start();\n                        }\n                    }[\"useAgentConversation.useCallback[playAudio]\"]);\n                } catch (pcmError) {\n                    console.log('PCM playback failed, trying different sample rates:', pcmError);\n                    // Try different sample rates\n                    const sampleRates = [\n                        24000,\n                        16000,\n                        44100,\n                        48000\n                    ];\n                    for (const sampleRate of sampleRates){\n                        try {\n                            const channels = 1;\n                            const bytesPerSample = 2;\n                            const numSamples = arrayBuffer.byteLength / bytesPerSample;\n                            const audioBuffer = audioContextRef.current.createBuffer(channels, numSamples, sampleRate);\n                            const channelData = audioBuffer.getChannelData(0);\n                            const dataView = new DataView(arrayBuffer);\n                            for(let i = 0; i < numSamples; i++){\n                                const sample = dataView.getInt16(i * 2, true);\n                                channelData[i] = sample / 32768.0;\n                            }\n                            const source = audioContextRef.current.createBufferSource();\n                            source.buffer = audioBuffer;\n                            source.connect(audioContextRef.current.destination);\n                            console.log(\"Playing with sample rate: \".concat(sampleRate, \"Hz\"));\n                            return new Promise({\n                                \"useAgentConversation.useCallback[playAudio]\": (resolve)=>{\n                                    source.onended = ({\n                                        \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                            console.log(\"Audio played successfully at \".concat(sampleRate, \"Hz\"));\n                                            resolve();\n                                        }\n                                    })[\"useAgentConversation.useCallback[playAudio]\"];\n                                    source.start();\n                                }\n                            }[\"useAgentConversation.useCallback[playAudio]\"]);\n                        } catch (rateError) {\n                            console.log(\"Sample rate \".concat(sampleRate, \"Hz failed, trying next...\"));\n                            continue;\n                        }\n                    }\n                    throw new Error('All sample rates failed');\n                }\n            } catch (error) {\n                console.error('Error playing audio:', error);\n                console.log('Base64 sample:', base64Audio.substring(0, 50));\n            }\n        }\n    }[\"useAgentConversation.useCallback[playAudio]\"], [\n        initAudioContext\n    ]);\n    // Process audio queue\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingRef.current || audioQueueRef.current.length === 0) return;\n            isPlayingRef.current = true;\n            const audioData = audioQueueRef.current.shift();\n            if (audioData) {\n                try {\n                    await playAudio(audioData);\n                } catch (error) {\n                    console.error('Error in processAudioQueue:', error);\n                // Continue processing even if one audio chunk fails\n                }\n            }\n            isPlayingRef.current = false;\n            // Process next audio in queue\n            if (audioQueueRef.current.length > 0) {\n                setTimeout({\n                    \"useAgentConversation.useCallback[processAudioQueue]\": ()=>processAudioQueue()\n                }[\"useAgentConversation.useCallback[processAudioQueue]\"], 50); // Small delay between chunks\n            }\n        }\n    }[\"useAgentConversation.useCallback[processAudioQueue]\"], [\n        playAudio\n    ]);\n    // Play audio chunks directly without complex buffering\n    const handleAudioChunk = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[handleAudioChunk]\": (base64Audio)=>{\n            console.log('Adding audio chunk to queue, length:', base64Audio.length);\n            // Add to queue for sequential playback\n            audioQueueRef.current.push(base64Audio);\n            // Start processing queue if not already playing\n            if (!isPlayingRef.current) {\n                processAudioQueue();\n            }\n        }\n    }[\"useAgentConversation.useCallback[handleAudioChunk]\"], [\n        processAudioQueue\n    ]);\n    const startConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n            if (isConnected) return;\n            try {\n                // Request microphone permission\n                await navigator.mediaDevices.getUserMedia({\n                    audio: true\n                });\n                // Initialize audio context for playback\n                await initAudioContext();\n                // Get signed URL for WebSocket connection\n                const response = await fetch(\"/api/conversation/signed-url?agentId=\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to get signed URL');\n                }\n                const { signed_url } = await response.json();\n                const websocket = new WebSocket(signed_url);\n                websocket.onopen = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        sendMessage(websocket, {\n                            type: \"conversation_initiation_client_data\"\n                        });\n                        await startStreaming();\n                        setIsListening(true);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onmessage = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async (event)=>{\n                        const data = JSON.parse(event.data);\n                        // Handle ping events to keep connection alive\n                        if (data.type === \"ping\") {\n                            setTimeout({\n                                \"useAgentConversation.useCallback[startConversation]\": ()=>{\n                                    sendMessage(websocket, {\n                                        type: \"pong\",\n                                        event_id: data.ping_event.event_id\n                                    });\n                                }\n                            }[\"useAgentConversation.useCallback[startConversation]\"], data.ping_event.ping_ms || 0);\n                        }\n                        if (data.type === \"user_transcript\") {\n                            const { user_transcription_event } = data;\n                            setUserTranscript(user_transcription_event.user_transcript);\n                            console.log(\"User transcript:\", user_transcription_event.user_transcript);\n                        }\n                        if (data.type === \"agent_response\") {\n                            const { agent_response_event } = data;\n                            setAgentResponse(agent_response_event.agent_response);\n                            console.log(\"Agent response:\", agent_response_event.agent_response);\n                        }\n                        if (data.type === \"interruption\") {\n                            console.log(\"Conversation interrupted\");\n                            // Clear audio queue on interruption\n                            audioQueueRef.current = [];\n                            isPlayingRef.current = false;\n                        }\n                        if (data.type === \"audio\") {\n                            const { audio_event } = data;\n                            console.log('Received audio chunk, length:', audio_event.audio_base_64.length);\n                            // Debug: Check audio format by looking at base64 header\n                            const base64Sample = audio_event.audio_base_64.substring(0, 50);\n                            console.log('Audio base64 sample:', base64Sample);\n                            // Try to detect format from base64 header\n                            try {\n                                const binaryString = atob(audio_event.audio_base_64.substring(0, 20));\n                                const header = Array.from(binaryString).map({\n                                    \"useAgentConversation.useCallback[startConversation].header\": (c)=>c.charCodeAt(0).toString(16).padStart(2, '0')\n                                }[\"useAgentConversation.useCallback[startConversation].header\"]).join(' ');\n                                console.log('Audio header bytes:', header);\n                            } catch (e) {\n                                console.log('Could not decode header');\n                            }\n                            // Buffer audio chunks instead of playing immediately\n                            bufferAudioChunk(audio_event.audio_base_64);\n                        }\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocketRef.current = websocket;\n                websocket.onclose = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket disconnected');\n                        websocketRef.current = null;\n                        setIsConnected(false);\n                        setIsListening(false);\n                        stopStreaming();\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onerror = ({\n                    \"useAgentConversation.useCallback[startConversation]\": (error)=>{\n                        console.error('WebSocket error:', error);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                throw error;\n            }\n        }\n    }[\"useAgentConversation.useCallback[startConversation]\"], [\n        agentId,\n        isConnected,\n        startStreaming,\n        initAudioContext,\n        bufferAudioChunk\n    ]);\n    const stopConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[stopConversation]\": async ()=>{\n            if (!websocketRef.current) return;\n            websocketRef.current.close();\n            setUserTranscript('');\n            setAgentResponse('');\n            audioQueueRef.current = [];\n            audioBufferRef.current = [];\n            isPlayingRef.current = false;\n            // Clear buffer timeout\n            if (bufferTimeoutRef.current) {\n                clearTimeout(bufferTimeoutRef.current);\n                bufferTimeoutRef.current = null;\n            }\n        }\n    }[\"useAgentConversation.useCallback[stopConversation]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAgentConversation.useEffect\": ()=>{\n            return ({\n                \"useAgentConversation.useEffect\": ()=>{\n                    if (websocketRef.current) {\n                        websocketRef.current.close();\n                    }\n                }\n            })[\"useAgentConversation.useEffect\"];\n        }\n    }[\"useAgentConversation.useEffect\"], []);\n    return {\n        startConversation,\n        stopConversation,\n        isConnected,\n        isListening,\n        userTranscript,\n        agentResponse\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAgentConversation.ts\n"));

/***/ })

});
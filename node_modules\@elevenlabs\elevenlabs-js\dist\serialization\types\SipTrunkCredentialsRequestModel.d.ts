/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SipTrunkCredentialsRequestModel: core.serialization.ObjectSchema<serializers.SipTrunkCredentialsRequestModel.Raw, ElevenLabs.SipTrunkCredentialsRequestModel>;
export declare namespace SipTrunkCredentialsRequestModel {
    interface Raw {
        username: string;
        password: string;
    }
}

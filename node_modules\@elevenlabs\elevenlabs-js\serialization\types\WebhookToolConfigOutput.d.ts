/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { WebhookToolApiSchemaConfigOutput } from "./WebhookToolApiSchemaConfigOutput";
import { DynamicVariablesConfig } from "./DynamicVariablesConfig";
export declare const WebhookToolConfigOutput: core.serialization.ObjectSchema<serializers.WebhookToolConfigOutput.Raw, ElevenLabs.WebhookToolConfigOutput>;
export declare namespace WebhookToolConfigOutput {
    interface Raw {
        name: string;
        description: string;
        response_timeout_secs?: number | null;
        api_schema: WebhookToolApiSchemaConfigOutput.Raw;
        dynamic_variables?: DynamicVariablesConfig.Raw | null;
    }
}

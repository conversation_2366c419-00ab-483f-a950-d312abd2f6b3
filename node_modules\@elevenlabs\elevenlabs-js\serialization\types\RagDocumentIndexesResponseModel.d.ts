/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { RagDocumentIndexResponseModel } from "./RagDocumentIndexResponseModel";
export declare const RagDocumentIndexesResponseModel: core.serialization.ObjectSchema<serializers.RagDocumentIndexesResponseModel.Raw, ElevenLabs.RagDocumentIndexesResponseModel>;
export declare namespace RagDocumentIndexesResponseModel {
    interface Raw {
        indexes: RagDocumentIndexResponseModel.Raw[];
    }
}

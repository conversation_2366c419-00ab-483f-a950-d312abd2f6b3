/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface Voice {
    /** The ID of the voice. */
    voiceId: string;
    /** The name of the voice. */
    name?: string;
    /** List of samples associated with the voice. */
    samples?: ElevenLabs.VoiceSample[];
    /** The category of the voice. */
    category?: ElevenLabs.VoiceResponseModelCategory;
    /** Fine-tuning information for the voice. */
    fineTuning?: ElevenLabs.FineTuningResponse;
    /** Labels associated with the voice. */
    labels?: Record<string, string>;
    /** The description of the voice. */
    description?: string;
    /** The preview URL of the voice. */
    previewUrl?: string;
    /** The tiers the voice is available for. */
    availableForTiers?: string[];
    /** The settings of the voice. */
    settings?: ElevenLabs.VoiceSettings;
    /** The sharing information of the voice. */
    sharing?: ElevenLabs.VoiceSharingResponse;
    /** The base model IDs for high-quality voices. */
    highQualityBaseModelIds?: string[];
    /** The verified languages of the voice. */
    verifiedLanguages?: ElevenLabs.VerifiedVoiceLanguageResponseModel[];
    /** The safety controls of the voice. */
    safetyControl?: ElevenLabs.VoiceResponseModelSafetyControl;
    /** The voice verification of the voice. */
    voiceVerification?: ElevenLabs.VoiceVerificationResponse;
    /** The permission on the resource of the voice. */
    permissionOnResource?: string;
    /** Whether the voice is owned by the user. */
    isOwner?: boolean;
    /** Whether the voice is legacy. */
    isLegacy?: boolean;
    /** Whether the voice is mixed. */
    isMixed?: boolean;
    /** The creation time of the voice in Unix time. */
    createdAtUnix?: number;
}

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as ElevenLabs from "../../../../../../api/index";
import * as core from "../../../../../../core";
import { WorkspaceGroupByNameResponseModel } from "../../../../../types/WorkspaceGroupByNameResponseModel";
export declare const Response: core.serialization.Schema<serializers.workspace.groups.search.Response.Raw, ElevenLabs.WorkspaceGroupByNameResponseModel[]>;
export declare namespace Response {
    type Raw = WorkspaceGroupByNameResponseModel.Raw[];
}

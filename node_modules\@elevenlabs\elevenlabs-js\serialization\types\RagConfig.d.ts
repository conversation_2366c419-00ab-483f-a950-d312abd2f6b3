/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { EmbeddingModelEnum } from "./EmbeddingModelEnum";
export declare const RagConfig: core.serialization.ObjectSchema<serializers.RagConfig.Raw, ElevenLabs.RagConfig>;
export declare namespace RagConfig {
    interface Raw {
        enabled?: boolean | null;
        embedding_model?: EmbeddingModelEnum.Raw | null;
        max_vector_distance?: number | null;
        max_documents_length?: number | null;
        max_retrieved_rag_chunks_count?: number | null;
    }
}

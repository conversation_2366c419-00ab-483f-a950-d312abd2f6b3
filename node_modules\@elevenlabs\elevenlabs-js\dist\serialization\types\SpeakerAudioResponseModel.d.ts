/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SpeakerAudioResponseModel: core.serialization.ObjectSchema<serializers.SpeakerAudioResponseModel.Raw, ElevenLabs.SpeakerAudioResponseModel>;
export declare namespace SpeakerAudioResponseModel {
    interface Raw {
        audio_base_64: string;
        media_type: string;
        duration_secs: number;
    }
}

export var downsampleBuffer = function (buffer, sampleRate, outSampleRate) {
    if (outSampleRate === sampleRate) {
        var result_1 = new Int16Array(buffer.length);
        for (var i = 0; i < buffer.length; i++) {
            var s = Math.max(-1, Math.min(1, buffer[i]));
            result_1[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
        }
        return result_1;
    }
    var sampleRateRatio = sampleRate / outSampleRate;
    var newLength = Math.round(buffer.length / sampleRateRatio);
    var result = new Int16Array(newLength);
    var offsetResult = 0;
    var offsetBuffer = 0;
    while (offsetResult < newLength) {
        var nextOffsetBuffer = Math.round((offsetResult + 1) * sampleRateRatio);
        var accum = 0;
        var count = 0;
        for (var i = offsetBuffer; i < nextOffsetBuffer && i < buffer.length; i++) {
            accum += buffer[i];
            count++;
        }
        var avg = accum / count;
        var s = Math.max(-1, Math.min(1, avg));
        result[offsetResult] = s < 0 ? s * 0x8000 : s * 0x7fff;
        offsetResult++;
        offsetBuffer = nextOffsetBuffer;
    }
    return result;
};

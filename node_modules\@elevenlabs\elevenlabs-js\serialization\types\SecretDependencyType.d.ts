/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SecretDependencyType: core.serialization.Schema<serializers.SecretDependencyType.Raw, ElevenLabs.SecretDependencyType>;
export declare namespace SecretDependencyType {
    type Raw = "conversation_initiation_webhook";
}

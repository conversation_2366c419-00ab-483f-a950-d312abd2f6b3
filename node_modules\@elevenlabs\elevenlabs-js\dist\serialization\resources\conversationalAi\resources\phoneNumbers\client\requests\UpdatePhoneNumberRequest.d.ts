/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../../index";
import * as ElevenLabs from "../../../../../../../api/index";
import * as core from "../../../../../../../core";
export declare const UpdatePhoneNumberRequest: core.serialization.Schema<serializers.conversationalAi.UpdatePhoneNumberRequest.Raw, ElevenLabs.conversationalAi.UpdatePhoneNumberRequest>;
export declare namespace UpdatePhoneNumberRequest {
    interface Raw {
        agent_id?: string | null;
    }
}

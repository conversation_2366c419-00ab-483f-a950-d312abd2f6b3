/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SegmentTranscriptionResponse: core.serialization.ObjectSchema<serializers.SegmentTranscriptionResponse.Raw, ElevenLabs.SegmentTranscriptionResponse>;
export declare namespace SegmentTranscriptionResponse {
    interface Raw {
        version: number;
    }
}

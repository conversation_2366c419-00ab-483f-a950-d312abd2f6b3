/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { RealtimeVoiceSettings } from "./RealtimeVoiceSettings";
import { GenerationConfig } from "./GenerationConfig";
import { PronunciationDictionaryLocator } from "./PronunciationDictionaryLocator";
export declare const WebsocketTtsClientMessageMulti: core.serialization.ObjectSchema<serializers.WebsocketTtsClientMessageMulti.Raw, ElevenLabs.WebsocketTtsClientMessageMulti>;
export declare namespace WebsocketTtsClientMessageMulti {
    interface Raw {
        text?: string | null;
        voice_settings?: RealtimeVoiceSettings.Raw | null;
        generation_config?: GenerationConfig.Raw | null;
        "xi-api-key"?: string | null;
        authorization?: string | null;
        flush?: boolean | null;
        pronunciation_dictionary_locators?: PronunciationDictionaryLocator.Raw[] | null;
        contextId?: string | null;
        close_context?: boolean | null;
        close_socket?: boolean | null;
    }
}

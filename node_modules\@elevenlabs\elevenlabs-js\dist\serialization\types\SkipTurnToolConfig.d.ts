/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SkipTurnToolConfig: core.serialization.ObjectSchema<serializers.SkipTurnToolConfig.Raw, ElevenLabs.SkipTurnToolConfig>;
export declare namespace SkipTurnToolConfig {
    interface Raw {
    }
}

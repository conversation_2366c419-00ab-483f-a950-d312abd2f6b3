"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[agentId]/page",{

/***/ "(app-pages-browser)/./src/app/chat/[agentId]/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/chat/[agentId]/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VoiceChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useAgentConversation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAgentConversation */ \"(app-pages-browser)/./src/hooks/useAgentConversation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction VoiceChatPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const agentId = params.agentId;\n    const [agentName, setAgentName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('AI Agent');\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { startConversation, stopConversation, isConnected, isListening, userTranscript, agentResponse } = (0,_hooks_useAgentConversation__WEBPACK_IMPORTED_MODULE_3__.useAgentConversation)(agentId);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"VoiceChatPage.useEffect\": ()=>{\n            // Fetch agent info to get the name\n            fetchAgentInfo();\n        }\n    }[\"VoiceChatPage.useEffect\"], [\n        agentId\n    ]);\n    const fetchAgentInfo = async ()=>{\n        try {\n            const response = await fetch('/api/agents');\n            if (response.ok) {\n                const data = await response.json();\n                const agent = data.agents.find((a)=>a.agentId === agentId);\n                if (agent) {\n                    setAgentName(agent.name);\n                }\n            }\n        } catch (error) {\n            console.error('Error fetching agent info:', error);\n        }\n    };\n    const handleStartConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VoiceChatPage.useCallback[handleStartConversation]\": async ()=>{\n            try {\n                setError(null);\n                await startConversation();\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                setError('Failed to start conversation. Please check your microphone permissions.');\n            }\n        }\n    }[\"VoiceChatPage.useCallback[handleStartConversation]\"], [\n        startConversation\n    ]);\n    const handleStopConversation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"VoiceChatPage.useCallback[handleStopConversation]\": async ()=>{\n            try {\n                await stopConversation();\n            } catch (error) {\n                console.error('Failed to stop conversation:', error);\n            }\n        }\n    }[\"VoiceChatPage.useCallback[handleStopConversation]\"], [\n        stopConversation\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/'),\n                                    className: \"text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200\",\n                                    children: \"← Back\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-gray-800 dark:text-white\",\n                                    children: [\n                                        \"Voice Chat with \",\n                                        agentName\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-red-500')\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                    children: isConnected ? 'Connected' : 'Disconnected'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col items-center justify-center p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-2xl w-full text-center space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-32 h-32 mx-auto rounded-full border-4 transition-all duration-300 \".concat(isListening ? 'border-green-500 bg-green-100 dark:bg-green-900 animate-pulse' : 'border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-800'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-12 h-12 \".concat(isListening ? 'text-green-600' : 'text-gray-400'),\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                isListening && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 w-32 h-32 mx-auto rounded-full border-4 border-green-300 animate-ping\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-800 dark:text-white\",\n                                    children: isConnected ? isListening ? 'Listening...' : 'Ready to chat' : 'Not connected'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-4\",\n                            children: !isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStartConversation,\n                                className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-8 rounded-full text-lg transition-colors duration-200 flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Start Voice Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleStopConversation,\n                                className: \"bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-8 rounded-full text-lg transition-colors duration-200 flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Stop Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this),\n                        (userTranscript || agentResponse) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg space-y-4\",\n                            children: [\n                                userTranscript && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: \"You said:\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-800 dark:text-white bg-blue-50 dark:bg-blue-900 p-3 rounded\",\n                                            children: userTranscript\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 17\n                                }, this),\n                                agentResponse && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: [\n                                                agentName,\n                                                \" responded:\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-800 dark:text-white bg-green-50 dark:bg-green-900 p-3 rounded\",\n                                            children: agentResponse\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 dark:text-gray-400 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        '\\uD83C\\uDFA4 Click \"Start Voice Chat\" to begin talking with ',\n                                        agentName\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"\\uD83D\\uDD0A Make sure your microphone and speakers are working\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"\\uD83D\\uDCAC Speak naturally - the AI will respond with voice\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\prototype mas muji\\\\prototype\\\\src\\\\app\\\\chat\\\\[agentId]\\\\page.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(VoiceChatPage, \"IoLXzTOw95+YV4KZUxacimscDpE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useAgentConversation__WEBPACK_IMPORTED_MODULE_3__.useAgentConversation\n    ];\n});\n_c = VoiceChatPage;\nvar _c;\n$RefreshReg$(_c, \"VoiceChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/[agentId]/page.tsx\n"));

/***/ })

});
/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SafetyRule: core.serialization.Schema<serializers.SafetyRule.Raw, ElevenLabs.SafetyRule>;
export declare namespace SafetyRule {
    type Raw = "sexual_minors" | "forget_moderation" | "extremism" | "scam_fraud" | "political" | "self_harm" | "illegal_distribution_medical" | "sexual_adults" | "unknown";
}

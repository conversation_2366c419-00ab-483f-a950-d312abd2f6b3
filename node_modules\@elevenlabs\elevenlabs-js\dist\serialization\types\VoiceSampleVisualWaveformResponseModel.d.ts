/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const VoiceSampleVisualWaveformResponseModel: core.serialization.ObjectSchema<serializers.VoiceSampleVisualWaveformResponseModel.Raw, ElevenLabs.VoiceSampleVisualWaveformResponseModel>;
export declare namespace VoiceSampleVisualWaveformResponseModel {
    interface Raw {
        sample_id: string;
        visual_waveform: number[];
    }
}

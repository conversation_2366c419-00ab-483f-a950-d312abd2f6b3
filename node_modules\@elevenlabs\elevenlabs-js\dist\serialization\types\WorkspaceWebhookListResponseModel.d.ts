/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { WorkspaceWebhookResponseModel } from "./WorkspaceWebhookResponseModel";
export declare const WorkspaceWebhookListResponseModel: core.serialization.ObjectSchema<serializers.WorkspaceWebhookListResponseModel.Raw, ElevenLabs.WorkspaceWebhookListResponseModel>;
export declare namespace WorkspaceWebhookListResponseModel {
    interface Raw {
        webhooks: WorkspaceWebhookResponseModel.Raw[];
    }
}

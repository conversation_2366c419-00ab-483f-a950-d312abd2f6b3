"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[agentId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useAgentConversation.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useAgentConversation.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAgentConversation: () => (/* binding */ useAgentConversation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var voice_stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! voice-stream */ \"(app-pages-browser)/../node_modules/voice-stream/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAgentConversation auto */ \n\nconst sendMessage = (websocket, request)=>{\n    if (websocket.readyState !== WebSocket.OPEN) {\n        return;\n    }\n    websocket.send(JSON.stringify(request));\n};\nconst useAgentConversation = (agentId)=>{\n    const websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [userTranscript, setUserTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [agentResponse, setAgentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isPlayingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const { startStreaming, stopStreaming } = (0,voice_stream__WEBPACK_IMPORTED_MODULE_1__.useVoiceStream)({\n        onAudioChunked: {\n            \"useAgentConversation.useVoiceStream\": (audioData)=>{\n                if (!websocketRef.current) return;\n                sendMessage(websocketRef.current, {\n                    user_audio_chunk: audioData\n                });\n            }\n        }[\"useAgentConversation.useVoiceStream\"]\n    });\n    // Initialize audio context\n    const initAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[initAudioContext]\": async ()=>{\n            if (!audioContextRef.current) {\n                try {\n                    const AudioContextClass = window.AudioContext || window.webkitAudioContext;\n                    audioContextRef.current = new AudioContextClass();\n                    // Resume audio context if it's suspended (required by some browsers)\n                    if (audioContextRef.current.state === 'suspended') {\n                        await audioContextRef.current.resume();\n                    }\n                } catch (error) {\n                    console.error('Failed to initialize audio context:', error);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[initAudioContext]\"], []);\n    // Play audio from base64 using HTML Audio element\n    const playAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[playAudio]\": async (base64Audio)=>{\n            try {\n                // Create audio element with base64 data URL\n                const audio = new Audio(\"data:audio/mpeg;base64,\".concat(base64Audio));\n                audio.preload = 'auto';\n                return new Promise({\n                    \"useAgentConversation.useCallback[playAudio]\": (resolve, reject)=>{\n                        audio.onended = ({\n                            \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                resolve();\n                            }\n                        })[\"useAgentConversation.useCallback[playAudio]\"];\n                        audio.onerror = ({\n                            \"useAgentConversation.useCallback[playAudio]\": (error)=>{\n                                console.error('Audio playback error:', error);\n                                reject(error);\n                            }\n                        })[\"useAgentConversation.useCallback[playAudio]\"];\n                        audio.oncanplaythrough = ({\n                            \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                audio.play().catch(reject);\n                            }\n                        })[\"useAgentConversation.useCallback[playAudio]\"];\n                        // Fallback: try to play immediately if canplaythrough doesn't fire\n                        setTimeout({\n                            \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                if (audio.readyState >= 2) {\n                                    audio.play().catch(reject);\n                                }\n                            }\n                        }[\"useAgentConversation.useCallback[playAudio]\"], 100);\n                    }\n                }[\"useAgentConversation.useCallback[playAudio]\"]);\n            } catch (error) {\n                console.error('Error creating audio element:', error);\n            }\n        }\n    }[\"useAgentConversation.useCallback[playAudio]\"], []);\n    // Process audio queue\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingRef.current || audioQueueRef.current.length === 0) return;\n            isPlayingRef.current = true;\n            const audioData = audioQueueRef.current.shift();\n            if (audioData) {\n                await playAudio(audioData);\n            }\n            isPlayingRef.current = false;\n            // Process next audio in queue\n            if (audioQueueRef.current.length > 0) {\n                processAudioQueue();\n            }\n        }\n    }[\"useAgentConversation.useCallback[processAudioQueue]\"], [\n        playAudio\n    ]);\n    const startConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n            if (isConnected) return;\n            try {\n                // Request microphone permission\n                await navigator.mediaDevices.getUserMedia({\n                    audio: true\n                });\n                // Initialize audio context\n                await initAudioContext();\n                // Get signed URL for WebSocket connection\n                const response = await fetch(\"/api/conversation/signed-url?agentId=\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to get signed URL');\n                }\n                const { signed_url } = await response.json();\n                const websocket = new WebSocket(signed_url);\n                websocket.onopen = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        sendMessage(websocket, {\n                            type: \"conversation_initiation_client_data\"\n                        });\n                        await startStreaming();\n                        setIsListening(true);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onmessage = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async (event)=>{\n                        const data = JSON.parse(event.data);\n                        // Handle ping events to keep connection alive\n                        if (data.type === \"ping\") {\n                            setTimeout({\n                                \"useAgentConversation.useCallback[startConversation]\": ()=>{\n                                    sendMessage(websocket, {\n                                        type: \"pong\",\n                                        event_id: data.ping_event.event_id\n                                    });\n                                }\n                            }[\"useAgentConversation.useCallback[startConversation]\"], data.ping_event.ping_ms || 0);\n                        }\n                        if (data.type === \"user_transcript\") {\n                            const { user_transcription_event } = data;\n                            setUserTranscript(user_transcription_event.user_transcript);\n                            console.log(\"User transcript:\", user_transcription_event.user_transcript);\n                        }\n                        if (data.type === \"agent_response\") {\n                            const { agent_response_event } = data;\n                            setAgentResponse(agent_response_event.agent_response);\n                            console.log(\"Agent response:\", agent_response_event.agent_response);\n                        }\n                        if (data.type === \"interruption\") {\n                            console.log(\"Conversation interrupted\");\n                            // Clear audio queue on interruption\n                            audioQueueRef.current = [];\n                            isPlayingRef.current = false;\n                        }\n                        if (data.type === \"audio\") {\n                            const { audio_event } = data;\n                            console.log('Received audio chunk, length:', audio_event.audio_base_64.length);\n                            // Add audio to queue for sequential playback\n                            audioQueueRef.current.push(audio_event.audio_base_64);\n                            processAudioQueue();\n                        }\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocketRef.current = websocket;\n                websocket.onclose = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket disconnected');\n                        websocketRef.current = null;\n                        setIsConnected(false);\n                        setIsListening(false);\n                        stopStreaming();\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onerror = ({\n                    \"useAgentConversation.useCallback[startConversation]\": (error)=>{\n                        console.error('WebSocket error:', error);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                throw error;\n            }\n        }\n    }[\"useAgentConversation.useCallback[startConversation]\"], [\n        agentId,\n        isConnected,\n        startStreaming,\n        initAudioContext,\n        processAudioQueue\n    ]);\n    const stopConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[stopConversation]\": async ()=>{\n            if (!websocketRef.current) return;\n            websocketRef.current.close();\n            setUserTranscript('');\n            setAgentResponse('');\n            audioQueueRef.current = [];\n            isPlayingRef.current = false;\n        }\n    }[\"useAgentConversation.useCallback[stopConversation]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAgentConversation.useEffect\": ()=>{\n            return ({\n                \"useAgentConversation.useEffect\": ()=>{\n                    if (websocketRef.current) {\n                        websocketRef.current.close();\n                    }\n                }\n            })[\"useAgentConversation.useEffect\"];\n        }\n    }[\"useAgentConversation.useEffect\"], []);\n    return {\n        startConversation,\n        stopConversation,\n        isConnected,\n        isListening,\n        userTranscript,\n        agentResponse\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAgentConversation.ts\n"));

/***/ })

});
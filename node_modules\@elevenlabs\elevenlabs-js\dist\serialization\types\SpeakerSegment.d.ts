/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { DubbedSegment } from "./DubbedSegment";
export declare const SpeakerSegment: core.serialization.ObjectSchema<serializers.SpeakerSegment.Raw, ElevenLabs.SpeakerSegment>;
export declare namespace SpeakerSegment {
    interface Raw {
        id: string;
        start_time: number;
        end_time: number;
        text: string;
        dubs: Record<string, DubbedSegment.Raw>;
    }
}

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../../index";
import * as ElevenLabs from "../../../../../../../api/index";
import * as core from "../../../../../../../core";
import { BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole } from "../../types/BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole";
export declare const UpdateMemberRequest: core.serialization.Schema<serializers.workspace.UpdateMemberRequest.Raw, ElevenLabs.workspace.UpdateMemberRequest>;
export declare namespace UpdateMemberRequest {
    interface Raw {
        email: string;
        is_locked?: boolean | null;
        workspace_role?: BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole.Raw | null;
    }
}

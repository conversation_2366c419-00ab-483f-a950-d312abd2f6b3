/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { OrbAvatar } from "./OrbAvatar";
import { UrlAvatar } from "./UrlAvatar";
import { ImageAvatar } from "./ImageAvatar";
export declare const WidgetConfigInputAvatar: core.serialization.Schema<serializers.WidgetConfigInputAvatar.Raw, ElevenLabs.WidgetConfigInputAvatar>;
export declare namespace WidgetConfigInputAvatar {
    type Raw = WidgetConfigInputAvatar.Orb | WidgetConfigInputAvatar.Url | WidgetConfigInputAvatar.Image;
    interface Orb extends OrbAvatar.Raw {
        type: "orb";
    }
    interface Url extends UrlAvatar.Raw {
        type: "url";
    }
    interface Image extends ImageAvatar.Raw {
        type: "image";
    }
}

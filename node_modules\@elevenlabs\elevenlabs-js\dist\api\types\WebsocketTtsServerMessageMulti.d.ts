/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * Message sent from the server to the client for the multi-context TTS WebSocket.
 */
export interface WebsocketTtsServerMessageMulti {
    /** A generated partial audio chunk, encoded using the selected output_format (e.g., MP3 as a base64 string). */
    audio?: string;
    /** If true, indicates that this is the final message for the specified contextId. This is sent when a context is closed. `audio` will be null or empty. */
    isFinal?: boolean;
    normalizedAlignment?: ElevenLabs.NormalizedAlignment;
    alignment?: ElevenLabs.Alignment;
    /** The context identifier to which this message pertains. */
    contextId?: string;
}

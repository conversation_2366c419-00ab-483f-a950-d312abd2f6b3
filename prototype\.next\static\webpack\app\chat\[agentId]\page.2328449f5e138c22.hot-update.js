"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[agentId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useAgentConversation.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useAgentConversation.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAgentConversation: () => (/* binding */ useAgentConversation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var voice_stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! voice-stream */ \"(app-pages-browser)/../node_modules/voice-stream/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAgentConversation auto */ \n\nconst sendMessage = (websocket, request)=>{\n    if (websocket.readyState !== WebSocket.OPEN) {\n        return;\n    }\n    websocket.send(JSON.stringify(request));\n};\nconst useAgentConversation = (agentId)=>{\n    const websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [userTranscript, setUserTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [agentResponse, setAgentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isPlayingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const audioBufferRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const bufferTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { startStreaming, stopStreaming } = (0,voice_stream__WEBPACK_IMPORTED_MODULE_1__.useVoiceStream)({\n        onAudioChunked: {\n            \"useAgentConversation.useVoiceStream\": (audioData)=>{\n                if (!websocketRef.current) return;\n                sendMessage(websocketRef.current, {\n                    user_audio_chunk: audioData\n                });\n            }\n        }[\"useAgentConversation.useVoiceStream\"]\n    });\n    // Initialize audio context\n    const initAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[initAudioContext]\": async ()=>{\n            if (!audioContextRef.current) {\n                try {\n                    const AudioContextClass = window.AudioContext || window.webkitAudioContext;\n                    audioContextRef.current = new AudioContextClass();\n                    // Resume audio context if it's suspended (required by some browsers)\n                    if (audioContextRef.current.state === 'suspended') {\n                        await audioContextRef.current.resume();\n                    }\n                } catch (error) {\n                    console.error('Failed to initialize audio context:', error);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[initAudioContext]\"], []);\n    // Play audio from base64 using Web Audio API\n    const playAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[playAudio]\": async (base64Audio)=>{\n            try {\n                console.log('Attempting to play audio chunk, length:', base64Audio.length);\n                // Initialize audio context if needed\n                if (!audioContextRef.current) {\n                    await initAudioContext();\n                }\n                if (!audioContextRef.current) {\n                    throw new Error('Audio context not available');\n                }\n                // Method 1: Try Web Audio API with proper decoding\n                try {\n                    // Convert base64 to ArrayBuffer\n                    const binaryString = atob(base64Audio);\n                    const arrayBuffer = new ArrayBuffer(binaryString.length);\n                    const uint8Array = new Uint8Array(arrayBuffer);\n                    for(let i = 0; i < binaryString.length; i++){\n                        uint8Array[i] = binaryString.charCodeAt(i);\n                    }\n                    console.log('Decoded audio buffer size:', arrayBuffer.byteLength);\n                    // Decode audio data\n                    const audioBuffer = await audioContextRef.current.decodeAudioData(arrayBuffer);\n                    console.log('Audio buffer decoded successfully:', audioBuffer.duration, 'seconds');\n                    // Create and play audio source\n                    const source = audioContextRef.current.createBufferSource();\n                    source.buffer = audioBuffer;\n                    source.connect(audioContextRef.current.destination);\n                    return new Promise({\n                        \"useAgentConversation.useCallback[playAudio]\": (resolve)=>{\n                            source.onended = ({\n                                \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                    console.log('Audio playback completed');\n                                    resolve();\n                                }\n                            })[\"useAgentConversation.useCallback[playAudio]\"];\n                            source.start();\n                        }\n                    }[\"useAgentConversation.useCallback[playAudio]\"]);\n                } catch (webAudioError) {\n                    console.log('Web Audio API failed, trying Blob approach:', webAudioError);\n                    // Method 2: Try Blob with different MIME types\n                    const mimeTypes = [\n                        'audio/mpeg',\n                        'audio/mp3',\n                        'audio/wav',\n                        'audio/ogg',\n                        'audio/webm',\n                        'audio/mp4',\n                        'audio/aac'\n                    ];\n                    for (const mimeType of mimeTypes){\n                        try {\n                            // Convert base64 to Blob\n                            const binaryString = atob(base64Audio);\n                            const bytes = new Uint8Array(binaryString.length);\n                            for(let i = 0; i < binaryString.length; i++){\n                                bytes[i] = binaryString.charCodeAt(i);\n                            }\n                            const blob = new Blob([\n                                bytes\n                            ], {\n                                type: mimeType\n                            });\n                            const audioUrl = URL.createObjectURL(blob);\n                            const audio = new Audio(audioUrl);\n                            audio.volume = 0.8;\n                            const playPromise = new Promise({\n                                \"useAgentConversation.useCallback[playAudio]\": (resolve, reject)=>{\n                                    let resolved = false;\n                                    const cleanup = {\n                                        \"useAgentConversation.useCallback[playAudio].cleanup\": ()=>{\n                                            URL.revokeObjectURL(audioUrl);\n                                            audio.onended = null;\n                                            audio.onerror = null;\n                                            audio.oncanplaythrough = null;\n                                        }\n                                    }[\"useAgentConversation.useCallback[playAudio].cleanup\"];\n                                    audio.onended = ({\n                                        \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                            if (!resolved) {\n                                                resolved = true;\n                                                cleanup();\n                                                console.log(\"Audio played successfully with MIME type: \".concat(mimeType));\n                                                resolve();\n                                            }\n                                        }\n                                    })[\"useAgentConversation.useCallback[playAudio]\"];\n                                    audio.onerror = ({\n                                        \"useAgentConversation.useCallback[playAudio]\": (error)=>{\n                                            if (!resolved) {\n                                                cleanup();\n                                                reject(error);\n                                            }\n                                        }\n                                    })[\"useAgentConversation.useCallback[playAudio]\"];\n                                    audio.oncanplaythrough = ({\n                                        \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                            console.log(\"Audio ready with MIME type: \".concat(mimeType));\n                                            audio.play().catch(reject);\n                                        }\n                                    })[\"useAgentConversation.useCallback[playAudio]\"];\n                                    // Fallback\n                                    setTimeout({\n                                        \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                            if (!resolved && audio.readyState >= 2) {\n                                                audio.play().catch(reject);\n                                            }\n                                        }\n                                    }[\"useAgentConversation.useCallback[playAudio]\"], 300);\n                                }\n                            }[\"useAgentConversation.useCallback[playAudio]\"]);\n                            await playPromise;\n                            return; // Success\n                        } catch (blobError) {\n                            console.log(\"MIME type \".concat(mimeType, \" failed:\"), blobError);\n                            continue;\n                        }\n                    }\n                    throw new Error('All playback methods failed');\n                }\n            } catch (error) {\n                console.error('Error playing audio:', error);\n                // Log first few characters of base64 for debugging\n                console.log('Base64 sample:', base64Audio.substring(0, 100));\n            }\n        }\n    }[\"useAgentConversation.useCallback[playAudio]\"], [\n        initAudioContext\n    ]);\n    // Process audio queue\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingRef.current || audioQueueRef.current.length === 0) return;\n            isPlayingRef.current = true;\n            const audioData = audioQueueRef.current.shift();\n            if (audioData) {\n                try {\n                    await playAudio(audioData);\n                } catch (error) {\n                    console.error('Error in processAudioQueue:', error);\n                // Continue processing even if one audio chunk fails\n                }\n            }\n            isPlayingRef.current = false;\n            // Process next audio in queue\n            if (audioQueueRef.current.length > 0) {\n                setTimeout({\n                    \"useAgentConversation.useCallback[processAudioQueue]\": ()=>processAudioQueue()\n                }[\"useAgentConversation.useCallback[processAudioQueue]\"], 50); // Small delay between chunks\n            }\n        }\n    }[\"useAgentConversation.useCallback[processAudioQueue]\"], [\n        playAudio\n    ]);\n    // Buffer audio chunks and play when ready\n    const bufferAudioChunk = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[bufferAudioChunk]\": (base64Audio)=>{\n            try {\n                // Decode base64 to binary data\n                const binaryString = atob(base64Audio);\n                const bytes = new Uint8Array(binaryString.length);\n                for(let i = 0; i < binaryString.length; i++){\n                    bytes[i] = binaryString.charCodeAt(i);\n                }\n                // Add decoded bytes to buffer\n                audioBufferRef.current.push(bytes);\n                console.log('Added audio chunk to buffer, chunks count:', audioBufferRef.current.length);\n                // Clear existing timeout\n                if (bufferTimeoutRef.current) {\n                    clearTimeout(bufferTimeoutRef.current);\n                }\n                // Set timeout to play buffered audio\n                bufferTimeoutRef.current = setTimeout({\n                    \"useAgentConversation.useCallback[bufferAudioChunk]\": ()=>{\n                        if (audioBufferRef.current.length > 0) {\n                            // Concatenate all buffered chunks\n                            const totalLength = audioBufferRef.current.reduce({\n                                \"useAgentConversation.useCallback[bufferAudioChunk].totalLength\": (sum, chunk)=>sum + chunk.length\n                            }[\"useAgentConversation.useCallback[bufferAudioChunk].totalLength\"], 0);\n                            const combinedBuffer = new Uint8Array(totalLength);\n                            let offset = 0;\n                            for (const chunk of audioBufferRef.current){\n                                combinedBuffer.set(chunk, offset);\n                                offset += chunk.length;\n                            }\n                            console.log('Playing buffered audio, total bytes:', totalLength);\n                            // Convert back to base64 for playback\n                            const combinedBase64 = btoa(String.fromCharCode(...combinedBuffer));\n                            audioQueueRef.current.push(combinedBase64);\n                            audioBufferRef.current = []; // Clear buffer\n                            processAudioQueue();\n                        }\n                    }\n                }[\"useAgentConversation.useCallback[bufferAudioChunk]\"], 300); // Wait 300ms for more chunks\n            } catch (error) {\n                console.error('Error buffering audio chunk:', error);\n            }\n        }\n    }[\"useAgentConversation.useCallback[bufferAudioChunk]\"], [\n        processAudioQueue\n    ]);\n    const startConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n            if (isConnected) return;\n            try {\n                // Request microphone permission\n                await navigator.mediaDevices.getUserMedia({\n                    audio: true\n                });\n                // Initialize audio context for playback\n                await initAudioContext();\n                // Get signed URL for WebSocket connection\n                const response = await fetch(\"/api/conversation/signed-url?agentId=\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to get signed URL');\n                }\n                const { signed_url } = await response.json();\n                const websocket = new WebSocket(signed_url);\n                websocket.onopen = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        sendMessage(websocket, {\n                            type: \"conversation_initiation_client_data\"\n                        });\n                        await startStreaming();\n                        setIsListening(true);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onmessage = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async (event)=>{\n                        const data = JSON.parse(event.data);\n                        // Handle ping events to keep connection alive\n                        if (data.type === \"ping\") {\n                            setTimeout({\n                                \"useAgentConversation.useCallback[startConversation]\": ()=>{\n                                    sendMessage(websocket, {\n                                        type: \"pong\",\n                                        event_id: data.ping_event.event_id\n                                    });\n                                }\n                            }[\"useAgentConversation.useCallback[startConversation]\"], data.ping_event.ping_ms || 0);\n                        }\n                        if (data.type === \"user_transcript\") {\n                            const { user_transcription_event } = data;\n                            setUserTranscript(user_transcription_event.user_transcript);\n                            console.log(\"User transcript:\", user_transcription_event.user_transcript);\n                        }\n                        if (data.type === \"agent_response\") {\n                            const { agent_response_event } = data;\n                            setAgentResponse(agent_response_event.agent_response);\n                            console.log(\"Agent response:\", agent_response_event.agent_response);\n                        }\n                        if (data.type === \"interruption\") {\n                            console.log(\"Conversation interrupted\");\n                            // Clear audio queue on interruption\n                            audioQueueRef.current = [];\n                            isPlayingRef.current = false;\n                        }\n                        if (data.type === \"audio\") {\n                            const { audio_event } = data;\n                            console.log('Received audio chunk, length:', audio_event.audio_base_64.length);\n                            // Debug: Check audio format by looking at base64 header\n                            const base64Sample = audio_event.audio_base_64.substring(0, 50);\n                            console.log('Audio base64 sample:', base64Sample);\n                            // Try to detect format from base64 header\n                            try {\n                                const binaryString = atob(audio_event.audio_base_64.substring(0, 20));\n                                const header = Array.from(binaryString).map({\n                                    \"useAgentConversation.useCallback[startConversation].header\": (c)=>c.charCodeAt(0).toString(16).padStart(2, '0')\n                                }[\"useAgentConversation.useCallback[startConversation].header\"]).join(' ');\n                                console.log('Audio header bytes:', header);\n                            } catch (e) {\n                                console.log('Could not decode header');\n                            }\n                            // Buffer audio chunks instead of playing immediately\n                            bufferAudioChunk(audio_event.audio_base_64);\n                        }\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocketRef.current = websocket;\n                websocket.onclose = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket disconnected');\n                        websocketRef.current = null;\n                        setIsConnected(false);\n                        setIsListening(false);\n                        stopStreaming();\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onerror = ({\n                    \"useAgentConversation.useCallback[startConversation]\": (error)=>{\n                        console.error('WebSocket error:', error);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                throw error;\n            }\n        }\n    }[\"useAgentConversation.useCallback[startConversation]\"], [\n        agentId,\n        isConnected,\n        startStreaming,\n        initAudioContext,\n        bufferAudioChunk\n    ]);\n    const stopConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[stopConversation]\": async ()=>{\n            if (!websocketRef.current) return;\n            websocketRef.current.close();\n            setUserTranscript('');\n            setAgentResponse('');\n            audioQueueRef.current = [];\n            audioBufferRef.current = '';\n            isPlayingRef.current = false;\n            // Clear buffer timeout\n            if (bufferTimeoutRef.current) {\n                clearTimeout(bufferTimeoutRef.current);\n                bufferTimeoutRef.current = null;\n            }\n        }\n    }[\"useAgentConversation.useCallback[stopConversation]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAgentConversation.useEffect\": ()=>{\n            return ({\n                \"useAgentConversation.useEffect\": ()=>{\n                    if (websocketRef.current) {\n                        websocketRef.current.close();\n                    }\n                }\n            })[\"useAgentConversation.useEffect\"];\n        }\n    }[\"useAgentConversation.useEffect\"], []);\n    return {\n        startConversation,\n        stopConversation,\n        isConnected,\n        isListening,\n        userTranscript,\n        agentResponse\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAgentConversation.ts\n"));

/***/ })

});
/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface VoiceSamplePreviewResponseModel {
    /** The base64 encoded audio. */
    audioBase64: string;
    /** The ID of the voice. */
    voiceId: string;
    /** The ID of the sample. */
    sampleId: string;
    /** The media type of the audio. */
    mediaType: string;
    /** The duration of the audio in seconds. */
    durationSecs?: number;
}

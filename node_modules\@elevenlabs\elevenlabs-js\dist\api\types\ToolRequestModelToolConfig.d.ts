/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * Configuration for the tool
 */
export type ToolRequestModelToolConfig = ElevenLabs.ToolRequestModelToolConfig.Client | ElevenLabs.ToolRequestModelToolConfig.Mcp | ElevenLabs.ToolRequestModelToolConfig.System | ElevenLabs.ToolRequestModelToolConfig.Webhook;
export declare namespace ToolRequestModelToolConfig {
    interface Client extends ElevenLabs.ClientToolConfigInput {
        type: "client";
    }
    interface Mcp extends ElevenLabs.McpToolConfigInput {
        type: "mcp";
    }
    interface System extends ElevenLabs.SystemToolConfigInput {
        type: "system";
    }
    interface Webhook extends ElevenLabs.WebhookToolConfigInput {
        type: "webhook";
    }
}

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const RealtimeVoiceSettings: core.serialization.ObjectSchema<serializers.RealtimeVoiceSettings.Raw, ElevenLabs.RealtimeVoiceSettings>;
export declare namespace RealtimeVoiceSettings {
    interface Raw {
        stability?: number | null;
        similarity_boost?: number | null;
        style?: number | null;
        use_speaker_boost?: boolean | null;
        speed?: number | null;
    }
}

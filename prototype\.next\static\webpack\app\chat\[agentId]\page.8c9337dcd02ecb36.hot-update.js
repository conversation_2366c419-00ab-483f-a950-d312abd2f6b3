"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[agentId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useAgentConversation.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useAgentConversation.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAgentConversation: () => (/* binding */ useAgentConversation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var voice_stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! voice-stream */ \"(app-pages-browser)/../node_modules/voice-stream/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAgentConversation auto */ \n\nconst sendMessage = (websocket, request)=>{\n    if (websocket.readyState !== WebSocket.OPEN) {\n        return;\n    }\n    websocket.send(JSON.stringify(request));\n};\nconst useAgentConversation = (agentId)=>{\n    const websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [userTranscript, setUserTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [agentResponse, setAgentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isPlayingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const audioBufferRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const bufferTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { startStreaming, stopStreaming } = (0,voice_stream__WEBPACK_IMPORTED_MODULE_1__.useVoiceStream)({\n        onAudioChunked: {\n            \"useAgentConversation.useVoiceStream\": (audioData)=>{\n                if (!websocketRef.current) return;\n                sendMessage(websocketRef.current, {\n                    user_audio_chunk: audioData\n                });\n            }\n        }[\"useAgentConversation.useVoiceStream\"]\n    });\n    // Initialize audio context\n    const initAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[initAudioContext]\": async ()=>{\n            if (!audioContextRef.current) {\n                try {\n                    const AudioContextClass = window.AudioContext || window.webkitAudioContext;\n                    audioContextRef.current = new AudioContextClass();\n                    // Resume audio context if it's suspended (required by some browsers)\n                    if (audioContextRef.current.state === 'suspended') {\n                        await audioContextRef.current.resume();\n                    }\n                } catch (error) {\n                    console.error('Failed to initialize audio context:', error);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[initAudioContext]\"], []);\n    // Play raw PCM audio from ElevenLabs\n    const playAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[playAudio]\": async (base64Audio)=>{\n            try {\n                console.log('Attempting to play audio chunk, length:', base64Audio.length);\n                // Initialize audio context if needed\n                if (!audioContextRef.current) {\n                    await initAudioContext();\n                }\n                if (!audioContextRef.current) {\n                    throw new Error('Audio context not available');\n                }\n                // Convert base64 to ArrayBuffer\n                const binaryString = atob(base64Audio);\n                const arrayBuffer = new ArrayBuffer(binaryString.length);\n                const uint8Array = new Uint8Array(arrayBuffer);\n                for(let i = 0; i < binaryString.length; i++){\n                    uint8Array[i] = binaryString.charCodeAt(i);\n                }\n                console.log('🎵 Processing raw PCM audio data...');\n                // ElevenLabs sends raw PCM data - create AudioBuffer manually\n                // Common ElevenLabs settings: 16-bit PCM, mono, 24kHz\n                const sampleRate = 24000; // ElevenLabs default\n                const channels = 1; // mono\n                const bytesPerSample = 2; // 16-bit\n                const numSamples = arrayBuffer.byteLength / bytesPerSample;\n                // Create AudioBuffer\n                const audioBuffer = audioContextRef.current.createBuffer(channels, numSamples, sampleRate);\n                const channelData = audioBuffer.getChannelData(0);\n                // Convert 16-bit PCM to float32 (-1 to 1)\n                const dataView = new DataView(arrayBuffer);\n                for(let i = 0; i < numSamples; i++){\n                    const sample = dataView.getInt16(i * 2, true); // little-endian\n                    channelData[i] = sample / 32768.0; // Convert to -1 to 1 range\n                }\n                console.log(\"✅ Created PCM audio buffer: \".concat(audioBuffer.duration.toFixed(2), \"s at \").concat(sampleRate, \"Hz\"));\n                // Play the audio\n                const source = audioContextRef.current.createBufferSource();\n                source.buffer = audioBuffer;\n                source.connect(audioContextRef.current.destination);\n                return new Promise({\n                    \"useAgentConversation.useCallback[playAudio]\": (resolve)=>{\n                        source.onended = ({\n                            \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                console.log('✅ PCM audio playback completed');\n                                resolve();\n                            }\n                        })[\"useAgentConversation.useCallback[playAudio]\"];\n                        source.start();\n                    }\n                }[\"useAgentConversation.useCallback[playAudio]\"]);\n            } catch (error) {\n                console.error('Error playing PCM audio:', error);\n                console.log('Base64 sample:', base64Audio.substring(0, 50));\n                // Try different sample rate if 24kHz failed\n                try {\n                    console.log('🔄 Trying alternative sample rate (22050Hz)...');\n                    if (!audioContextRef.current) return;\n                    const binaryString = atob(base64Audio);\n                    const arrayBuffer = new ArrayBuffer(binaryString.length);\n                    const uint8Array = new Uint8Array(arrayBuffer);\n                    for(let i = 0; i < binaryString.length; i++){\n                        uint8Array[i] = binaryString.charCodeAt(i);\n                    }\n                    const sampleRate = 22050;\n                    const channels = 1;\n                    const bytesPerSample = 2;\n                    const numSamples = arrayBuffer.byteLength / bytesPerSample;\n                    const audioBuffer = audioContextRef.current.createBuffer(channels, numSamples, sampleRate);\n                    const channelData = audioBuffer.getChannelData(0);\n                    const dataView = new DataView(arrayBuffer);\n                    for(let i = 0; i < numSamples; i++){\n                        const sample = dataView.getInt16(i * 2, true);\n                        channelData[i] = sample / 32768.0;\n                    }\n                    const source = audioContextRef.current.createBufferSource();\n                    source.buffer = audioBuffer;\n                    source.connect(audioContextRef.current.destination);\n                    return new Promise({\n                        \"useAgentConversation.useCallback[playAudio]\": (resolve)=>{\n                            source.onended = ({\n                                \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                    console.log('✅ Alternative PCM audio playback completed');\n                                    resolve();\n                                }\n                            })[\"useAgentConversation.useCallback[playAudio]\"];\n                            source.start();\n                        }\n                    }[\"useAgentConversation.useCallback[playAudio]\"]);\n                } catch (fallbackError) {\n                    console.error('Fallback PCM playback also failed:', fallbackError);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[playAudio]\"], [\n        initAudioContext\n    ]);\n    // Process audio queue\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingRef.current || audioQueueRef.current.length === 0) return;\n            isPlayingRef.current = true;\n            const audioData = audioQueueRef.current.shift();\n            if (audioData) {\n                try {\n                    await playAudio(audioData);\n                } catch (error) {\n                    console.error('Error in processAudioQueue:', error);\n                // Continue processing even if one audio chunk fails\n                }\n            }\n            isPlayingRef.current = false;\n            // Process next audio in queue\n            if (audioQueueRef.current.length > 0) {\n                setTimeout({\n                    \"useAgentConversation.useCallback[processAudioQueue]\": ()=>processAudioQueue()\n                }[\"useAgentConversation.useCallback[processAudioQueue]\"], 50); // Small delay between chunks\n            }\n        }\n    }[\"useAgentConversation.useCallback[processAudioQueue]\"], [\n        playAudio\n    ]);\n    // Play audio chunks directly without complex buffering\n    const handleAudioChunk = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[handleAudioChunk]\": (base64Audio)=>{\n            console.log('Adding audio chunk to queue, length:', base64Audio.length);\n            // Add to queue for sequential playback\n            audioQueueRef.current.push(base64Audio);\n            // Start processing queue if not already playing\n            if (!isPlayingRef.current) {\n                processAudioQueue();\n            }\n        }\n    }[\"useAgentConversation.useCallback[handleAudioChunk]\"], [\n        processAudioQueue\n    ]);\n    const startConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n            if (isConnected) return;\n            try {\n                // Request microphone permission\n                await navigator.mediaDevices.getUserMedia({\n                    audio: true\n                });\n                // Initialize audio context for playback\n                await initAudioContext();\n                // Get signed URL for WebSocket connection\n                const response = await fetch(\"/api/conversation/signed-url?agentId=\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to get signed URL');\n                }\n                const { signed_url } = await response.json();\n                const websocket = new WebSocket(signed_url);\n                websocket.onopen = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        sendMessage(websocket, {\n                            type: \"conversation_initiation_client_data\"\n                        });\n                        await startStreaming();\n                        setIsListening(true);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onmessage = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async (event)=>{\n                        const data = JSON.parse(event.data);\n                        // Handle ping events to keep connection alive\n                        if (data.type === \"ping\") {\n                            setTimeout({\n                                \"useAgentConversation.useCallback[startConversation]\": ()=>{\n                                    sendMessage(websocket, {\n                                        type: \"pong\",\n                                        event_id: data.ping_event.event_id\n                                    });\n                                }\n                            }[\"useAgentConversation.useCallback[startConversation]\"], data.ping_event.ping_ms || 0);\n                        }\n                        if (data.type === \"user_transcript\") {\n                            const { user_transcription_event } = data;\n                            setUserTranscript(user_transcription_event.user_transcript);\n                            console.log(\"User transcript:\", user_transcription_event.user_transcript);\n                        }\n                        if (data.type === \"agent_response\") {\n                            const { agent_response_event } = data;\n                            setAgentResponse(agent_response_event.agent_response);\n                            console.log(\"Agent response:\", agent_response_event.agent_response);\n                        }\n                        if (data.type === \"interruption\") {\n                            console.log(\"Conversation interrupted\");\n                            // Clear audio queue on interruption\n                            audioQueueRef.current = [];\n                            isPlayingRef.current = false;\n                        }\n                        if (data.type === \"audio\") {\n                            const { audio_event } = data;\n                            console.log('Received audio chunk, length:', audio_event.audio_base_64.length);\n                            // Debug: Check audio format by looking at base64 header\n                            const base64Sample = audio_event.audio_base_64.substring(0, 50);\n                            console.log('Audio base64 sample:', base64Sample);\n                            // Try to detect format from base64 header\n                            try {\n                                const binaryString = atob(audio_event.audio_base_64.substring(0, 20));\n                                const header = Array.from(binaryString).map({\n                                    \"useAgentConversation.useCallback[startConversation].header\": (c)=>c.charCodeAt(0).toString(16).padStart(2, '0')\n                                }[\"useAgentConversation.useCallback[startConversation].header\"]).join(' ');\n                                console.log('Audio header bytes:', header);\n                            } catch (e) {\n                                console.log('Could not decode header');\n                            }\n                            // Handle audio chunk directly\n                            handleAudioChunk(audio_event.audio_base_64);\n                        }\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocketRef.current = websocket;\n                websocket.onclose = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket disconnected');\n                        websocketRef.current = null;\n                        setIsConnected(false);\n                        setIsListening(false);\n                        stopStreaming();\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onerror = ({\n                    \"useAgentConversation.useCallback[startConversation]\": (error)=>{\n                        console.error('WebSocket error:', error);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                throw error;\n            }\n        }\n    }[\"useAgentConversation.useCallback[startConversation]\"], [\n        agentId,\n        isConnected,\n        startStreaming,\n        initAudioContext,\n        handleAudioChunk\n    ]);\n    const stopConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[stopConversation]\": async ()=>{\n            if (!websocketRef.current) return;\n            websocketRef.current.close();\n            setUserTranscript('');\n            setAgentResponse('');\n            audioQueueRef.current = [];\n            isPlayingRef.current = false;\n        }\n    }[\"useAgentConversation.useCallback[stopConversation]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAgentConversation.useEffect\": ()=>{\n            return ({\n                \"useAgentConversation.useEffect\": ()=>{\n                    if (websocketRef.current) {\n                        websocketRef.current.close();\n                    }\n                }\n            })[\"useAgentConversation.useEffect\"];\n        }\n    }[\"useAgentConversation.useEffect\"], []);\n    return {\n        startConversation,\n        stopConversation,\n        isConnected,\n        isListening,\n        userTranscript,\n        agentResponse\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAgentConversation.ts\n"));

/***/ })

});
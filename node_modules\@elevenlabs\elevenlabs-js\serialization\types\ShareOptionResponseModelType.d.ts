/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ShareOptionResponseModelType: core.serialization.Schema<serializers.ShareOptionResponseModelType.Raw, ElevenLabs.ShareOptionResponseModelType>;
export declare namespace ShareOptionResponseModelType {
    type Raw = "user" | "group" | "key";
}

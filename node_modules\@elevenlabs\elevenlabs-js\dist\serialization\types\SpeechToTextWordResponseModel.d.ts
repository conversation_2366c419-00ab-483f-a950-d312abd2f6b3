/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { SpeechToTextWordResponseModelType } from "./SpeechToTextWordResponseModelType";
import { SpeechToTextCharacterResponseModel } from "./SpeechToTextCharacterResponseModel";
export declare const SpeechToTextWordResponseModel: core.serialization.ObjectSchema<serializers.SpeechToTextWordResponseModel.Raw, ElevenLabs.SpeechToTextWordResponseModel>;
export declare namespace SpeechToTextWordResponseModel {
    interface Raw {
        text: string;
        start?: number | null;
        end?: number | null;
        type: SpeechToTextWordResponseModelType.Raw;
        speaker_id?: string | null;
        logprob: number;
        characters?: SpeechToTextCharacterResponseModel.Raw[] | null;
    }
}

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SegmentUpdateResponse: core.serialization.ObjectSchema<serializers.SegmentUpdateResponse.Raw, ElevenLabs.SegmentUpdateResponse>;
export declare namespace SegmentUpdateResponse {
    interface Raw {
        version: number;
    }
}

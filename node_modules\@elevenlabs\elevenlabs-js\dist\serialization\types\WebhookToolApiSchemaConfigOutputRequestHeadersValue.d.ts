/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ConvAiSecretLocator } from "./ConvAiSecretLocator";
import { ConvAiDynamicVariable } from "./ConvAiDynamicVariable";
export declare const WebhookToolApiSchemaConfigOutputRequestHeadersValue: core.serialization.Schema<serializers.WebhookToolApiSchemaConfigOutputRequestHeadersValue.Raw, ElevenLabs.WebhookToolApiSchemaConfigOutputRequestHeadersValue>;
export declare namespace WebhookToolApiSchemaConfigOutputRequestHeadersValue {
    type Raw = string | ConvAiSecretLocator.Raw | ConvAiDynamicVariable.Raw;
}

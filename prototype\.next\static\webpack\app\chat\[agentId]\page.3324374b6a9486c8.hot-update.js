"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[agentId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useAgentConversation.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useAgentConversation.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAgentConversation: () => (/* binding */ useAgentConversation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var voice_stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! voice-stream */ \"(app-pages-browser)/../node_modules/voice-stream/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAgentConversation auto */ \n\nconst sendMessage = (websocket, request)=>{\n    if (websocket.readyState !== WebSocket.OPEN) {\n        return;\n    }\n    websocket.send(JSON.stringify(request));\n};\nconst useAgentConversation = (agentId)=>{\n    const websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [userTranscript, setUserTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [agentResponse, setAgentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isPlayingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const { startStreaming, stopStreaming } = (0,voice_stream__WEBPACK_IMPORTED_MODULE_1__.useVoiceStream)({\n        onAudioChunked: {\n            \"useAgentConversation.useVoiceStream\": (audioData)=>{\n                if (!websocketRef.current) return;\n                sendMessage(websocketRef.current, {\n                    user_audio_chunk: audioData\n                });\n            }\n        }[\"useAgentConversation.useVoiceStream\"]\n    });\n    // Initialize audio context\n    const initAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[initAudioContext]\": ()=>{\n            if (!audioContextRef.current) {\n                audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();\n            }\n        }\n    }[\"useAgentConversation.useCallback[initAudioContext]\"], []);\n    // Play audio from base64\n    const playAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[playAudio]\": async (base64Audio)=>{\n            if (!audioContextRef.current) return;\n            try {\n                // Method 1: Try using HTML Audio element (more compatible)\n                const audioBlob = new Blob([\n                    Uint8Array.from(atob(base64Audio), {\n                        \"useAgentConversation.useCallback[playAudio]\": (c)=>c.charCodeAt(0)\n                    }[\"useAgentConversation.useCallback[playAudio]\"])\n                ], {\n                    type: 'audio/mpeg'\n                });\n                const audioUrl = URL.createObjectURL(audioBlob);\n                const audio = new Audio(audioUrl);\n                return new Promise({\n                    \"useAgentConversation.useCallback[playAudio]\": (resolve, reject)=>{\n                        audio.onended = ({\n                            \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                URL.revokeObjectURL(audioUrl);\n                                resolve();\n                            }\n                        })[\"useAgentConversation.useCallback[playAudio]\"];\n                        audio.onerror = ({\n                            \"useAgentConversation.useCallback[playAudio]\": (error)=>{\n                                URL.revokeObjectURL(audioUrl);\n                                console.error('Audio playback error:', error);\n                                reject(error);\n                            }\n                        })[\"useAgentConversation.useCallback[playAudio]\"];\n                        audio.play().catch(reject);\n                    }\n                }[\"useAgentConversation.useCallback[playAudio]\"]);\n            } catch (error) {\n                console.error('Error playing audio:', error);\n                // Fallback: Try Web Audio API with different approach\n                try {\n                    const response = await fetch(\"data:audio/mpeg;base64,\".concat(base64Audio));\n                    const arrayBuffer = await response.arrayBuffer();\n                    const audioBuffer = await audioContextRef.current.decodeAudioData(arrayBuffer);\n                    const source = audioContextRef.current.createBufferSource();\n                    source.buffer = audioBuffer;\n                    source.connect(audioContextRef.current.destination);\n                    return new Promise({\n                        \"useAgentConversation.useCallback[playAudio]\": (resolve)=>{\n                            source.onended = ({\n                                \"useAgentConversation.useCallback[playAudio]\": ()=>resolve()\n                            })[\"useAgentConversation.useCallback[playAudio]\"];\n                            source.start();\n                        }\n                    }[\"useAgentConversation.useCallback[playAudio]\"]);\n                } catch (fallbackError) {\n                    console.error('Fallback audio playback also failed:', fallbackError);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[playAudio]\"], []);\n    // Process audio queue\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingRef.current || audioQueueRef.current.length === 0) return;\n            isPlayingRef.current = true;\n            const audioData = audioQueueRef.current.shift();\n            if (audioData) {\n                await playAudio(audioData);\n            }\n            isPlayingRef.current = false;\n            // Process next audio in queue\n            if (audioQueueRef.current.length > 0) {\n                processAudioQueue();\n            }\n        }\n    }[\"useAgentConversation.useCallback[processAudioQueue]\"], [\n        playAudio\n    ]);\n    const startConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n            if (isConnected) return;\n            try {\n                // Request microphone permission\n                await navigator.mediaDevices.getUserMedia({\n                    audio: true\n                });\n                // Initialize audio context\n                initAudioContext();\n                // Get signed URL for WebSocket connection\n                const response = await fetch(\"/api/conversation/signed-url?agentId=\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to get signed URL');\n                }\n                const { signed_url } = await response.json();\n                const websocket = new WebSocket(signed_url);\n                websocket.onopen = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        sendMessage(websocket, {\n                            type: \"conversation_initiation_client_data\"\n                        });\n                        await startStreaming();\n                        setIsListening(true);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onmessage = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async (event)=>{\n                        const data = JSON.parse(event.data);\n                        // Handle ping events to keep connection alive\n                        if (data.type === \"ping\") {\n                            setTimeout({\n                                \"useAgentConversation.useCallback[startConversation]\": ()=>{\n                                    sendMessage(websocket, {\n                                        type: \"pong\",\n                                        event_id: data.ping_event.event_id\n                                    });\n                                }\n                            }[\"useAgentConversation.useCallback[startConversation]\"], data.ping_event.ping_ms || 0);\n                        }\n                        if (data.type === \"user_transcript\") {\n                            const { user_transcription_event } = data;\n                            setUserTranscript(user_transcription_event.user_transcript);\n                            console.log(\"User transcript:\", user_transcription_event.user_transcript);\n                        }\n                        if (data.type === \"agent_response\") {\n                            const { agent_response_event } = data;\n                            setAgentResponse(agent_response_event.agent_response);\n                            console.log(\"Agent response:\", agent_response_event.agent_response);\n                        }\n                        if (data.type === \"interruption\") {\n                            console.log(\"Conversation interrupted\");\n                            // Clear audio queue on interruption\n                            audioQueueRef.current = [];\n                            isPlayingRef.current = false;\n                        }\n                        if (data.type === \"audio\") {\n                            const { audio_event } = data;\n                            // Add audio to queue for sequential playback\n                            audioQueueRef.current.push(audio_event.audio_base_64);\n                            processAudioQueue();\n                        }\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocketRef.current = websocket;\n                websocket.onclose = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket disconnected');\n                        websocketRef.current = null;\n                        setIsConnected(false);\n                        setIsListening(false);\n                        stopStreaming();\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onerror = ({\n                    \"useAgentConversation.useCallback[startConversation]\": (error)=>{\n                        console.error('WebSocket error:', error);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                throw error;\n            }\n        }\n    }[\"useAgentConversation.useCallback[startConversation]\"], [\n        agentId,\n        isConnected,\n        startStreaming,\n        initAudioContext,\n        processAudioQueue\n    ]);\n    const stopConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[stopConversation]\": async ()=>{\n            if (!websocketRef.current) return;\n            websocketRef.current.close();\n            setUserTranscript('');\n            setAgentResponse('');\n            audioQueueRef.current = [];\n            isPlayingRef.current = false;\n        }\n    }[\"useAgentConversation.useCallback[stopConversation]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAgentConversation.useEffect\": ()=>{\n            return ({\n                \"useAgentConversation.useEffect\": ()=>{\n                    if (websocketRef.current) {\n                        websocketRef.current.close();\n                    }\n                }\n            })[\"useAgentConversation.useEffect\"];\n        }\n    }[\"useAgentConversation.useEffect\"], []);\n    return {\n        startConversation,\n        stopConversation,\n        isConnected,\n        isListening,\n        userTranscript,\n        agentResponse\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAgentConversation.ts\n"));

/***/ })

});
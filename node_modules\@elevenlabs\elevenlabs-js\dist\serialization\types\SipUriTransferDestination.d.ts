/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SipUriTransferDestination: core.serialization.ObjectSchema<serializers.SipUriTransferDestination.Raw, ElevenLabs.SipUriTransferDestination>;
export declare namespace SipUriTransferDestination {
    interface Raw {
        sip_uri: string;
    }
}

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { CharacterAlignmentResponseModel } from "./CharacterAlignmentResponseModel";
export declare const StreamingAudioChunkWithTimestampsResponse: core.serialization.ObjectSchema<serializers.StreamingAudioChunkWithTimestampsResponse.Raw, ElevenLabs.StreamingAudioChunkWithTimestampsResponse>;
export declare namespace StreamingAudioChunkWithTimestampsResponse {
    interface Raw {
        audio_base64: string;
        alignment?: CharacterAlignmentResponseModel.Raw | null;
        normalized_alignment?: CharacterAlignmentResponseModel.Raw | null;
    }
}

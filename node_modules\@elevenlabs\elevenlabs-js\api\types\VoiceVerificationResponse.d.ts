/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface VoiceVerificationResponse {
    /** Whether the voice requires verification. */
    requiresVerification: boolean;
    /** Whether the voice has been verified. */
    isVerified: boolean;
    /** List of verification failures. */
    verificationFailures: string[];
    /** The number of verification attempts. */
    verificationAttemptsCount: number;
    /** The language of the voice. */
    language?: string;
    /** Number of times a verification was attempted. */
    verificationAttempts?: ElevenLabs.VerificationAttemptResponse[];
}

/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../../../../index";
/**
 * @example
 *     {
 *         voiceDescription: "A sassy squeaky mouse"
 *     }
 */
export interface VoiceDesignRequestModel {
    /**
     * Output format of the generated audio. Formatted as codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires you to be subscribed to Pro tier or above. Note that the μ-law format (sometimes written mu-law, often approximated as u-law) is commonly used for Twilio audio inputs.
     */
    outputFormat?: ElevenLabs.TextToVoiceDesignRequestOutputFormat;
    /** Description to use for the created voice. */
    voiceDescription: string;
    /** Model to use for the voice generation. Possible values: eleven_multilingual_ttv_v2, eleven_ttv_v3. */
    modelId?: ElevenLabs.VoiceDesignRequestModelModelId;
    /** Text to generate, text length has to be between 100 and 1000. */
    text?: string;
    /** Whether to automatically generate a text suitable for the voice description. */
    autoGenerateText?: boolean;
    /** Controls the volume level of the generated voice. -1 is quietest, 1 is loudest, 0 corresponds to roughly -24 LUFS. */
    loudness?: number;
    /** Random number that controls the voice generation. Same seed with same inputs produces same voice. */
    seed?: number;
    /** Controls how closely the AI follows the prompt. Lower numbers give the AI more freedom to be creative, while higher numbers force it to stick more to the prompt. High numbers can cause voice to sound artificial or robotic. We recommend to use longer, more detailed prompts at lower Guidance Scale. */
    guidanceScale?: number;
    /** Determines whether the Text to Voice previews should be included in the response. If true, only the generated IDs will be returned which can then be streamed via the /v1/text-to-voice/:generated_voice_id/stream endpoint. */
    streamPreviews?: boolean;
    /** Higher quality results in better voice output but less variety. */
    quality?: number;
    /** Reference audio to use for the voice generation. The audio should be base64 encoded. Only supported when using the  eleven_ttv_v3 model. */
    referenceAudioBase64?: string;
    /** Controls the balance of prompt versus reference audio when generating voice samples. 0 means almost no prompt influence, 1 means almost no reference audio influence. Only supported when using the eleven_ttv_v3 model and providing reference audio. */
    promptStrength?: number;
}

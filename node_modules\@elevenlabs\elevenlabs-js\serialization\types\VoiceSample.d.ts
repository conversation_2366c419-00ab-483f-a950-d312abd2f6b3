/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { SpeakerSeparationResponseModel } from "./SpeakerSeparationResponseModel";
export declare const VoiceSample: core.serialization.ObjectSchema<serializers.VoiceSample.Raw, ElevenLabs.VoiceSample>;
export declare namespace VoiceSample {
    interface Raw {
        sample_id?: string | null;
        file_name?: string | null;
        mime_type?: string | null;
        size_bytes?: number | null;
        hash?: string | null;
        duration_secs?: number | null;
        remove_background_noise?: boolean | null;
        has_isolated_audio?: boolean | null;
        has_isolated_audio_preview?: boolean | null;
        speaker_separation?: SpeakerSeparationResponseModel.Raw | null;
        trim_start?: number | null;
        trim_end?: number | null;
    }
}

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { RenderType } from "./RenderType";
import { DubbingMediaReference } from "./DubbingMediaReference";
import { RenderStatus } from "./RenderStatus";
export declare const Render: core.serialization.ObjectSchema<serializers.Render.Raw, ElevenLabs.Render>;
export declare namespace Render {
    interface Raw {
        id: string;
        version: number;
        language?: string | null;
        type?: RenderType.Raw | null;
        media_ref?: DubbingMediaReference.Raw | null;
        status: RenderStatus.Raw;
    }
}

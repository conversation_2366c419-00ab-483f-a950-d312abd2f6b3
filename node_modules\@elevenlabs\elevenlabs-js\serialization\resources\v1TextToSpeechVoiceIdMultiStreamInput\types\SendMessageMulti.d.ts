/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as ElevenLabs from "../../../../api/index";
import * as core from "../../../../core";
import { InitializeConnectionMulti } from "../../../types/InitializeConnectionMulti";
import { InitialiseContext } from "../../../types/InitialiseContext";
import { SendTextMulti } from "../../../types/SendTextMulti";
import { FlushContext } from "../../../types/FlushContext";
import { CloseContext } from "../../../types/CloseContext";
import { CloseSocket } from "../../../types/CloseSocket";
import { KeepContextAlive } from "../../../types/KeepContextAlive";
export declare const SendMessageMulti: core.serialization.Schema<serializers.SendMessageMulti.Raw, ElevenLabs.SendMessageMulti>;
export declare namespace SendMessageMulti {
    type Raw = InitializeConnectionMulti.Raw | InitialiseContext.Raw | SendTextMulti.Raw | FlushContext.Raw | CloseContext.Raw | CloseSocket.Raw | KeepContextAlive.Raw;
}

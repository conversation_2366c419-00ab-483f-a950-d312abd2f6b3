/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SpeakerSeparationResponseModelStatus: core.serialization.Schema<serializers.SpeakerSeparationResponseModelStatus.Raw, ElevenLabs.SpeakerSeparationResponseModelStatus>;
export declare namespace SpeakerSeparationResponseModelStatus {
    type Raw = "not_started" | "pending" | "completed" | "failed";
}

'use client';

import { useCallback, useEffect, useRef, useState } from 'react';
import { useVoiceStream } from 'voice-stream';
import type { ElevenLabsWebSocketEvent } from '../types/websocket';

const sendMessage = (websocket: WebSocket, request: object) => {
  if (websocket.readyState !== WebSocket.OPEN) {
    return;
  }
  websocket.send(JSON.stringify(request));
};

export const useAgentConversation = (agentId: string) => {
  const websocketRef = useRef<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [userTranscript, setUserTranscript] = useState<string>('');
  const [agentResponse, setAgentResponse] = useState<string>('');
  const [isListening, setIsListening] = useState<boolean>(false);
  const audioContextRef = useRef<AudioContext | null>(null);
  const audioQueueRef = useRef<string[]>([]);
  const isPlayingRef = useRef<boolean>(false);

  const { startStreaming, stopStreaming } = useVoiceStream({
    onAudioChunked: (audioData) => {
      if (!websocketRef.current) return;
      sendMessage(websocketRef.current, {
        user_audio_chunk: audioData,
      });
    },
  });

  // Initialize audio context
  const initAudioContext = useCallback(async () => {
    if (!audioContextRef.current) {
      try {
        const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
        audioContextRef.current = new AudioContextClass();

        // Resume audio context if it's suspended (required by some browsers)
        if (audioContextRef.current.state === 'suspended') {
          await audioContextRef.current.resume();
        }
      } catch (error) {
        console.error('Failed to initialize audio context:', error);
      }
    }
  }, []);

  // Play audio from base64 using HTML Audio element
  const playAudio = useCallback(async (base64Audio: string) => {
    try {
      // Create audio element with base64 data URL
      const audio = new Audio(`data:audio/mpeg;base64,${base64Audio}`);
      audio.preload = 'auto';

      return new Promise<void>((resolve, reject) => {
        audio.onended = () => {
          resolve();
        };

        audio.onerror = (error) => {
          console.error('Audio playback error:', error);
          reject(error);
        };

        audio.oncanplaythrough = () => {
          audio.play().catch(reject);
        };

        // Fallback: try to play immediately if canplaythrough doesn't fire
        setTimeout(() => {
          if (audio.readyState >= 2) { // HAVE_CURRENT_DATA
            audio.play().catch(reject);
          }
        }, 100);
      });

    } catch (error) {
      console.error('Error creating audio element:', error);
    }
  }, []);

  // Process audio queue
  const processAudioQueue = useCallback(async () => {
    if (isPlayingRef.current || audioQueueRef.current.length === 0) return;
    
    isPlayingRef.current = true;
    const audioData = audioQueueRef.current.shift();
    
    if (audioData) {
      await playAudio(audioData);
    }
    
    isPlayingRef.current = false;
    
    // Process next audio in queue
    if (audioQueueRef.current.length > 0) {
      processAudioQueue();
    }
  }, [playAudio]);

  const startConversation = useCallback(async () => {
    if (isConnected) return;

    try {
      // Request microphone permission
      await navigator.mediaDevices.getUserMedia({ audio: true });

      // Get signed URL for WebSocket connection
      const response = await fetch(`/api/conversation/signed-url?agentId=${agentId}`);
      if (!response.ok) {
        throw new Error('Failed to get signed URL');
      }
      
      const { signed_url } = await response.json();
      const websocket = new WebSocket(signed_url);

      websocket.onopen = async () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        sendMessage(websocket, {
          type: "conversation_initiation_client_data",
        });
        await startStreaming();
        setIsListening(true);
      };

      websocket.onmessage = async (event) => {
        const data = JSON.parse(event.data) as ElevenLabsWebSocketEvent;

        // Handle ping events to keep connection alive
        if (data.type === "ping") {
          setTimeout(() => {
            sendMessage(websocket, {
              type: "pong",
              event_id: data.ping_event.event_id,
            });
          }, data.ping_event.ping_ms || 0);
        }

        if (data.type === "user_transcript") {
          const { user_transcription_event } = data;
          setUserTranscript(user_transcription_event.user_transcript);
          console.log("User transcript:", user_transcription_event.user_transcript);
        }

        if (data.type === "agent_response") {
          const { agent_response_event } = data;
          setAgentResponse(agent_response_event.agent_response);
          console.log("Agent response:", agent_response_event.agent_response);
        }

        if (data.type === "interruption") {
          console.log("Conversation interrupted");
          // Clear audio queue on interruption
          audioQueueRef.current = [];
          isPlayingRef.current = false;
        }

        if (data.type === "audio") {
          const { audio_event } = data;
          console.log('Received audio chunk, length:', audio_event.audio_base_64.length);

          // Add audio to queue for sequential playback
          audioQueueRef.current.push(audio_event.audio_base_64);
          processAudioQueue();
        }
      };

      websocketRef.current = websocket;

      websocket.onclose = async () => {
        console.log('WebSocket disconnected');
        websocketRef.current = null;
        setIsConnected(false);
        setIsListening(false);
        stopStreaming();
      };

      websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
      };

    } catch (error) {
      console.error('Failed to start conversation:', error);
      throw error;
    }
  }, [agentId, isConnected, startStreaming, initAudioContext, processAudioQueue]);

  const stopConversation = useCallback(async () => {
    if (!websocketRef.current) return;
    
    websocketRef.current.close();
    setUserTranscript('');
    setAgentResponse('');
    audioQueueRef.current = [];
    isPlayingRef.current = false;
  }, []);

  useEffect(() => {
    return () => {
      if (websocketRef.current) {
        websocketRef.current.close();
      }
    };
  }, []);

  return {
    startConversation,
    stopConversation,
    isConnected,
    isListening,
    userTranscript,
    agentResponse,
  };
};

'use client';

import { useCallback, useEffect, useRef, useState } from 'react';
import { useVoiceStream } from 'voice-stream';
import type { ElevenLabsWebSocketEvent } from '../types/websocket';

const sendMessage = (websocket: WebSocket, request: object) => {
  if (websocket.readyState !== WebSocket.OPEN) {
    return;
  }
  websocket.send(JSON.stringify(request));
};

export const useAgentConversation = (agentId: string) => {
  const websocketRef = useRef<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [userTranscript, setUserTranscript] = useState<string>('');
  const [agentResponse, setAgentResponse] = useState<string>('');
  const [isListening, setIsListening] = useState<boolean>(false);
  const audioContextRef = useRef<AudioContext | null>(null);
  const audioQueueRef = useRef<string[]>([]);
  const isPlayingRef = useRef<boolean>(false);
  const audioBufferRef = useRef<Uint8Array[]>([]);
  const bufferTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const workingSampleRateRef = useRef<number | null>(null);

  const { startStreaming, stopStreaming } = useVoiceStream({
    onAudioChunked: (audioData) => {
      if (!websocketRef.current) return;
      sendMessage(websocketRef.current, {
        user_audio_chunk: audioData,
      });
    },
  });

  // Initialize audio context
  const initAudioContext = useCallback(async () => {
    if (!audioContextRef.current) {
      try {
        const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
        audioContextRef.current = new AudioContextClass();

        // Resume audio context if it's suspended (required by some browsers)
        if (audioContextRef.current.state === 'suspended') {
          await audioContextRef.current.resume();
        }
      } catch (error) {
        console.error('Failed to initialize audio context:', error);
      }
    }
  }, []);

  // Play raw PCM audio from base64 using Web Audio API
  const playAudio = useCallback(async (base64Audio: string) => {
    try {
      console.log('Attempting to play audio chunk, length:', base64Audio.length);

      // Initialize audio context if needed
      if (!audioContextRef.current) {
        await initAudioContext();
      }

      if (!audioContextRef.current) {
        throw new Error('Audio context not available');
      }

      // Convert base64 to ArrayBuffer
      const binaryString = atob(base64Audio);
      const arrayBuffer = new ArrayBuffer(binaryString.length);
      const uint8Array = new Uint8Array(arrayBuffer);

      for (let i = 0; i < binaryString.length; i++) {
        uint8Array[i] = binaryString.charCodeAt(i);
      }

      console.log('Decoded audio buffer size:', arrayBuffer.byteLength);

      // ElevenLabs likely sends raw PCM data, so we need to create AudioBuffer manually
      try {
        // Use working sample rate if we found one, otherwise try different rates
        const sampleRatesToTry = workingSampleRateRef.current
          ? [workingSampleRateRef.current]
          : [8000, 11025, 16000, 22050, 24000, 44100, 48000];

        for (const sampleRate of sampleRatesToTry) {
          try {
            const channels = 1; // mono
            const bytesPerSample = 2; // 16-bit
            const numSamples = arrayBuffer.byteLength / bytesPerSample;

            // Create AudioBuffer with current sample rate
            const audioBuffer = audioContextRef.current.createBuffer(channels, numSamples, sampleRate);
            const channelData = audioBuffer.getChannelData(0);

            // Convert 16-bit PCM to float32 (-1 to 1)
            const dataView = new DataView(arrayBuffer);
            for (let i = 0; i < numSamples; i++) {
              const sample = dataView.getInt16(i * 2, true); // little-endian
              channelData[i] = sample / 32768.0; // Convert to -1 to 1 range
            }

            console.log(`Trying sample rate: ${sampleRate}Hz, duration: ${audioBuffer.duration.toFixed(2)}s`);

            // Play the audio
            const source = audioContextRef.current.createBufferSource();
            source.buffer = audioBuffer;
            source.connect(audioContextRef.current.destination);

            // Remember this sample rate for future chunks
            if (!workingSampleRateRef.current) {
              workingSampleRateRef.current = sampleRate;
              console.log(`🎯 Remembering sample rate: ${sampleRate}Hz for future chunks`);
            }

            return new Promise<void>((resolve) => {
              source.onended = () => {
                console.log(`✅ Audio played successfully at ${sampleRate}Hz (duration: ${audioBuffer.duration.toFixed(2)}s)`);
                resolve();
              };
              source.start();
            });

          } catch (rateError) {
            console.log(`❌ Sample rate ${sampleRate}Hz failed:`, rateError);
            continue;
          }
        }

        throw new Error('All sample rates failed');
      } catch (pcmError) {
        console.log('PCM playback failed:', pcmError);
        throw pcmError;
      }

    } catch (error) {
      console.error('Error playing audio:', error);
      console.log('Base64 sample:', base64Audio.substring(0, 50));
    }
  }, [initAudioContext]);

  // Process audio queue
  const processAudioQueue = useCallback(async () => {
    if (isPlayingRef.current || audioQueueRef.current.length === 0) return;

    isPlayingRef.current = true;
    const audioData = audioQueueRef.current.shift();

    if (audioData) {
      try {
        await playAudio(audioData);
      } catch (error) {
        console.error('Error in processAudioQueue:', error);
        // Continue processing even if one audio chunk fails
      }
    }

    isPlayingRef.current = false;

    // Process next audio in queue
    if (audioQueueRef.current.length > 0) {
      setTimeout(() => processAudioQueue(), 50); // Small delay between chunks
    }
  }, [playAudio]);

  // Play audio chunks directly without complex buffering
  const handleAudioChunk = useCallback((base64Audio: string) => {
    console.log('Adding audio chunk to queue, length:', base64Audio.length);

    // Add to queue for sequential playback
    audioQueueRef.current.push(base64Audio);

    // Start processing queue if not already playing
    if (!isPlayingRef.current) {
      processAudioQueue();
    }
  }, [processAudioQueue]);

  const startConversation = useCallback(async () => {
    if (isConnected) return;

    try {
      // Request microphone permission
      await navigator.mediaDevices.getUserMedia({ audio: true });

      // Initialize audio context for playback
      await initAudioContext();

      // Get signed URL for WebSocket connection
      const response = await fetch(`/api/conversation/signed-url?agentId=${agentId}`);
      if (!response.ok) {
        throw new Error('Failed to get signed URL');
      }
      
      const { signed_url } = await response.json();
      const websocket = new WebSocket(signed_url);

      websocket.onopen = async () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        sendMessage(websocket, {
          type: "conversation_initiation_client_data",
        });
        await startStreaming();
        setIsListening(true);
      };

      websocket.onmessage = async (event) => {
        const data = JSON.parse(event.data) as ElevenLabsWebSocketEvent;

        // Handle ping events to keep connection alive
        if (data.type === "ping") {
          setTimeout(() => {
            sendMessage(websocket, {
              type: "pong",
              event_id: data.ping_event.event_id,
            });
          }, data.ping_event.ping_ms || 0);
        }

        if (data.type === "user_transcript") {
          const { user_transcription_event } = data;
          setUserTranscript(user_transcription_event.user_transcript);
          console.log("User transcript:", user_transcription_event.user_transcript);
        }

        if (data.type === "agent_response") {
          const { agent_response_event } = data;
          setAgentResponse(agent_response_event.agent_response);
          console.log("Agent response:", agent_response_event.agent_response);
        }

        if (data.type === "interruption") {
          console.log("Conversation interrupted");
          // Clear audio queue on interruption
          audioQueueRef.current = [];
          isPlayingRef.current = false;
        }

        if (data.type === "audio") {
          const { audio_event } = data;
          console.log('Received audio chunk, length:', audio_event.audio_base_64.length);

          // Debug: Check audio format by looking at base64 header
          const base64Sample = audio_event.audio_base_64.substring(0, 50);
          console.log('Audio base64 sample:', base64Sample);

          // Try to detect format from base64 header
          try {
            const binaryString = atob(audio_event.audio_base_64.substring(0, 20));
            const header = Array.from(binaryString).map(c => c.charCodeAt(0).toString(16).padStart(2, '0')).join(' ');
            console.log('Audio header bytes:', header);
          } catch (e) {
            console.log('Could not decode header');
          }

          // Handle audio chunk directly
          handleAudioChunk(audio_event.audio_base_64);
        }
      };

      websocketRef.current = websocket;

      websocket.onclose = async () => {
        console.log('WebSocket disconnected');
        websocketRef.current = null;
        setIsConnected(false);
        setIsListening(false);
        stopStreaming();
      };

      websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
      };

    } catch (error) {
      console.error('Failed to start conversation:', error);
      throw error;
    }
  }, [agentId, isConnected, startStreaming, initAudioContext, handleAudioChunk]);

  const stopConversation = useCallback(async () => {
    if (!websocketRef.current) return;

    websocketRef.current.close();
    setUserTranscript('');
    setAgentResponse('');
    audioQueueRef.current = [];
    isPlayingRef.current = false;
  }, []);

  useEffect(() => {
    return () => {
      if (websocketRef.current) {
        websocketRef.current.close();
      }
    };
  }, []);

  return {
    startConversation,
    stopConversation,
    isConnected,
    isListening,
    userTranscript,
    agentResponse,
  };
};

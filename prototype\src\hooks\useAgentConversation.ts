'use client';

import { useCallback, useEffect, useRef, useState } from 'react';
import { useVoiceStream } from 'voice-stream';
import type { ElevenLabsWebSocketEvent } from '../types/websocket';

const sendMessage = (websocket: WebSocket, request: object) => {
  if (websocket.readyState !== WebSocket.OPEN) {
    return;
  }
  websocket.send(JSON.stringify(request));
};

export const useAgentConversation = (agentId: string) => {
  const websocketRef = useRef<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [userTranscript, setUserTranscript] = useState<string>('');
  const [agentResponse, setAgentResponse] = useState<string>('');
  const [isListening, setIsListening] = useState<boolean>(false);
  const audioContextRef = useRef<AudioContext | null>(null);
  const audioQueueRef = useRef<string[]>([]);
  const isPlayingRef = useRef<boolean>(false);
  const audioBufferRef = useRef<string>('');
  const bufferTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { startStreaming, stopStreaming } = useVoiceStream({
    onAudioChunked: (audioData) => {
      if (!websocketRef.current) return;
      sendMessage(websocketRef.current, {
        user_audio_chunk: audioData,
      });
    },
  });

  // Initialize audio context
  const initAudioContext = useCallback(async () => {
    if (!audioContextRef.current) {
      try {
        const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
        audioContextRef.current = new AudioContextClass();

        // Resume audio context if it's suspended (required by some browsers)
        if (audioContextRef.current.state === 'suspended') {
          await audioContextRef.current.resume();
        }
      } catch (error) {
        console.error('Failed to initialize audio context:', error);
      }
    }
  }, []);

  // Play audio from base64 using HTML Audio element
  const playAudio = useCallback(async (base64Audio: string) => {
    try {
      console.log('Attempting to play audio chunk, length:', base64Audio.length);

      // Try different audio formats that ElevenLabs might use
      const audioFormats = [
        'audio/mpeg',
        'audio/mp3',
        'audio/wav',
        'audio/ogg',
        'audio/webm'
      ];

      for (const format of audioFormats) {
        try {
          const audio = new Audio(`data:${format};base64,${base64Audio}`);
          audio.volume = 0.8;

          const playPromise = new Promise<void>((resolve, reject) => {
            let resolved = false;

            const cleanup = () => {
              audio.onended = null;
              audio.onerror = null;
              audio.oncanplaythrough = null;
              audio.onloadeddata = null;
            };

            audio.onended = () => {
              if (!resolved) {
                resolved = true;
                cleanup();
                console.log(`Audio played successfully with format: ${format}`);
                resolve();
              }
            };

            audio.onerror = (error) => {
              if (!resolved) {
                cleanup();
                console.log(`Failed to play with format ${format}:`, error);
                reject(error);
              }
            };

            audio.oncanplaythrough = () => {
              console.log(`Audio ready to play with format: ${format}`);
              audio.play().catch(reject);
            };

            // Fallback timeout
            setTimeout(() => {
              if (!resolved && audio.readyState >= 2) {
                audio.play().catch(reject);
              }
            }, 200);
          });

          await playPromise;
          return; // Success, exit the loop

        } catch (formatError) {
          console.log(`Format ${format} failed, trying next...`);
          continue;
        }
      }

      throw new Error('All audio formats failed');

    } catch (error) {
      console.error('Error playing audio with all formats:', error);
      // Don't throw, just log and continue
    }
  }, []);

  // Process audio queue
  const processAudioQueue = useCallback(async () => {
    if (isPlayingRef.current || audioQueueRef.current.length === 0) return;

    isPlayingRef.current = true;
    const audioData = audioQueueRef.current.shift();

    if (audioData) {
      try {
        await playAudio(audioData);
      } catch (error) {
        console.error('Error in processAudioQueue:', error);
        // Continue processing even if one audio chunk fails
      }
    }

    isPlayingRef.current = false;

    // Process next audio in queue
    if (audioQueueRef.current.length > 0) {
      setTimeout(() => processAudioQueue(), 50); // Small delay between chunks
    }
  }, [playAudio]);

  // Buffer audio chunks and play when ready
  const bufferAudioChunk = useCallback((base64Audio: string) => {
    // Add chunk to buffer
    audioBufferRef.current += base64Audio;

    // Clear existing timeout
    if (bufferTimeoutRef.current) {
      clearTimeout(bufferTimeoutRef.current);
    }

    // Set timeout to play buffered audio
    bufferTimeoutRef.current = setTimeout(() => {
      if (audioBufferRef.current.length > 0) {
        console.log('Playing buffered audio, total length:', audioBufferRef.current.length);
        audioQueueRef.current.push(audioBufferRef.current);
        audioBufferRef.current = ''; // Clear buffer
        processAudioQueue();
      }
    }, 500); // Wait 500ms for more chunks
  }, [processAudioQueue]);

  const startConversation = useCallback(async () => {
    if (isConnected) return;

    try {
      // Request microphone permission
      await navigator.mediaDevices.getUserMedia({ audio: true });

      // Get signed URL for WebSocket connection
      const response = await fetch(`/api/conversation/signed-url?agentId=${agentId}`);
      if (!response.ok) {
        throw new Error('Failed to get signed URL');
      }
      
      const { signed_url } = await response.json();
      const websocket = new WebSocket(signed_url);

      websocket.onopen = async () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        sendMessage(websocket, {
          type: "conversation_initiation_client_data",
        });
        await startStreaming();
        setIsListening(true);
      };

      websocket.onmessage = async (event) => {
        const data = JSON.parse(event.data) as ElevenLabsWebSocketEvent;

        // Handle ping events to keep connection alive
        if (data.type === "ping") {
          setTimeout(() => {
            sendMessage(websocket, {
              type: "pong",
              event_id: data.ping_event.event_id,
            });
          }, data.ping_event.ping_ms || 0);
        }

        if (data.type === "user_transcript") {
          const { user_transcription_event } = data;
          setUserTranscript(user_transcription_event.user_transcript);
          console.log("User transcript:", user_transcription_event.user_transcript);
        }

        if (data.type === "agent_response") {
          const { agent_response_event } = data;
          setAgentResponse(agent_response_event.agent_response);
          console.log("Agent response:", agent_response_event.agent_response);
        }

        if (data.type === "interruption") {
          console.log("Conversation interrupted");
          // Clear audio queue on interruption
          audioQueueRef.current = [];
          isPlayingRef.current = false;
        }

        if (data.type === "audio") {
          const { audio_event } = data;
          console.log('Received audio chunk, length:', audio_event.audio_base_64.length);

          // Buffer audio chunks instead of playing immediately
          bufferAudioChunk(audio_event.audio_base_64);
        }
      };

      websocketRef.current = websocket;

      websocket.onclose = async () => {
        console.log('WebSocket disconnected');
        websocketRef.current = null;
        setIsConnected(false);
        setIsListening(false);
        stopStreaming();
      };

      websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
      };

    } catch (error) {
      console.error('Failed to start conversation:', error);
      throw error;
    }
  }, [agentId, isConnected, startStreaming, initAudioContext, bufferAudioChunk]);

  const stopConversation = useCallback(async () => {
    if (!websocketRef.current) return;

    websocketRef.current.close();
    setUserTranscript('');
    setAgentResponse('');
    audioQueueRef.current = [];
    audioBufferRef.current = '';
    isPlayingRef.current = false;

    // Clear buffer timeout
    if (bufferTimeoutRef.current) {
      clearTimeout(bufferTimeoutRef.current);
      bufferTimeoutRef.current = null;
    }
  }, []);

  useEffect(() => {
    return () => {
      if (websocketRef.current) {
        websocketRef.current.close();
      }
    };
  }, []);

  return {
    startConversation,
    stopConversation,
    isConnected,
    isListening,
    userTranscript,
    agentResponse,
  };
};

'use client';

import { useCallback, useEffect, useRef, useState } from 'react';
import { useVoiceStream } from 'voice-stream';
import type { ElevenLabsWebSocketEvent } from '../types/websocket';

const sendMessage = (websocket: WebSocket, request: object) => {
  if (websocket.readyState !== WebSocket.OPEN) {
    return;
  }
  websocket.send(JSON.stringify(request));
};

export const useAgentConversation = (agentId: string) => {
  const websocketRef = useRef<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [userTranscript, setUserTranscript] = useState<string>('');
  const [agentResponse, setAgentResponse] = useState<string>('');
  const [isListening, setIsListening] = useState<boolean>(false);
  const audioContextRef = useRef<AudioContext | null>(null);
  const audioQueueRef = useRef<string[]>([]);
  const isPlayingRef = useRef<boolean>(false);
  const audioBufferRef = useRef<string>('');
  const bufferTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { startStreaming, stopStreaming } = useVoiceStream({
    onAudioChunked: (audioData) => {
      if (!websocketRef.current) return;
      sendMessage(websocketRef.current, {
        user_audio_chunk: audioData,
      });
    },
  });

  // Initialize audio context
  const initAudioContext = useCallback(async () => {
    if (!audioContextRef.current) {
      try {
        const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
        audioContextRef.current = new AudioContextClass();

        // Resume audio context if it's suspended (required by some browsers)
        if (audioContextRef.current.state === 'suspended') {
          await audioContextRef.current.resume();
        }
      } catch (error) {
        console.error('Failed to initialize audio context:', error);
      }
    }
  }, []);

  // Play audio from base64 using Web Audio API
  const playAudio = useCallback(async (base64Audio: string) => {
    try {
      console.log('Attempting to play audio chunk, length:', base64Audio.length);

      // Initialize audio context if needed
      if (!audioContextRef.current) {
        await initAudioContext();
      }

      if (!audioContextRef.current) {
        throw new Error('Audio context not available');
      }

      // Method 1: Try Web Audio API with proper decoding
      try {
        // Convert base64 to ArrayBuffer
        const binaryString = atob(base64Audio);
        const arrayBuffer = new ArrayBuffer(binaryString.length);
        const uint8Array = new Uint8Array(arrayBuffer);

        for (let i = 0; i < binaryString.length; i++) {
          uint8Array[i] = binaryString.charCodeAt(i);
        }

        console.log('Decoded audio buffer size:', arrayBuffer.byteLength);

        // Decode audio data
        const audioBuffer = await audioContextRef.current.decodeAudioData(arrayBuffer);
        console.log('Audio buffer decoded successfully:', audioBuffer.duration, 'seconds');

        // Create and play audio source
        const source = audioContextRef.current.createBufferSource();
        source.buffer = audioBuffer;
        source.connect(audioContextRef.current.destination);

        return new Promise<void>((resolve) => {
          source.onended = () => {
            console.log('Audio playback completed');
            resolve();
          };
          source.start();
        });

      } catch (webAudioError) {
        console.log('Web Audio API failed, trying Blob approach:', webAudioError);

        // Method 2: Try Blob with different MIME types
        const mimeTypes = [
          'audio/mpeg',
          'audio/mp3',
          'audio/wav',
          'audio/ogg',
          'audio/webm',
          'audio/mp4',
          'audio/aac'
        ];

        for (const mimeType of mimeTypes) {
          try {
            // Convert base64 to Blob
            const binaryString = atob(base64Audio);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
              bytes[i] = binaryString.charCodeAt(i);
            }

            const blob = new Blob([bytes], { type: mimeType });
            const audioUrl = URL.createObjectURL(blob);
            const audio = new Audio(audioUrl);
            audio.volume = 0.8;

            const playPromise = new Promise<void>((resolve, reject) => {
              let resolved = false;

              const cleanup = () => {
                URL.revokeObjectURL(audioUrl);
                audio.onended = null;
                audio.onerror = null;
                audio.oncanplaythrough = null;
              };

              audio.onended = () => {
                if (!resolved) {
                  resolved = true;
                  cleanup();
                  console.log(`Audio played successfully with MIME type: ${mimeType}`);
                  resolve();
                }
              };

              audio.onerror = (error) => {
                if (!resolved) {
                  cleanup();
                  reject(error);
                }
              };

              audio.oncanplaythrough = () => {
                console.log(`Audio ready with MIME type: ${mimeType}`);
                audio.play().catch(reject);
              };

              // Fallback
              setTimeout(() => {
                if (!resolved && audio.readyState >= 2) {
                  audio.play().catch(reject);
                }
              }, 300);
            });

            await playPromise;
            return; // Success

          } catch (blobError) {
            console.log(`MIME type ${mimeType} failed:`, blobError);
            continue;
          }
        }

        throw new Error('All playback methods failed');
      }

    } catch (error) {
      console.error('Error playing audio:', error);
      // Log first few characters of base64 for debugging
      console.log('Base64 sample:', base64Audio.substring(0, 100));
    }
  }, [initAudioContext]);

  // Process audio queue
  const processAudioQueue = useCallback(async () => {
    if (isPlayingRef.current || audioQueueRef.current.length === 0) return;

    isPlayingRef.current = true;
    const audioData = audioQueueRef.current.shift();

    if (audioData) {
      try {
        await playAudio(audioData);
      } catch (error) {
        console.error('Error in processAudioQueue:', error);
        // Continue processing even if one audio chunk fails
      }
    }

    isPlayingRef.current = false;

    // Process next audio in queue
    if (audioQueueRef.current.length > 0) {
      setTimeout(() => processAudioQueue(), 50); // Small delay between chunks
    }
  }, [playAudio]);

  // Buffer audio chunks and play when ready
  const bufferAudioChunk = useCallback((base64Audio: string) => {
    // Add chunk to buffer
    audioBufferRef.current += base64Audio;

    // Clear existing timeout
    if (bufferTimeoutRef.current) {
      clearTimeout(bufferTimeoutRef.current);
    }

    // Set timeout to play buffered audio
    bufferTimeoutRef.current = setTimeout(() => {
      if (audioBufferRef.current.length > 0) {
        console.log('Playing buffered audio, total length:', audioBufferRef.current.length);
        audioQueueRef.current.push(audioBufferRef.current);
        audioBufferRef.current = ''; // Clear buffer
        processAudioQueue();
      }
    }, 500); // Wait 500ms for more chunks
  }, [processAudioQueue]);

  const startConversation = useCallback(async () => {
    if (isConnected) return;

    try {
      // Request microphone permission
      await navigator.mediaDevices.getUserMedia({ audio: true });

      // Initialize audio context for playback
      await initAudioContext();

      // Get signed URL for WebSocket connection
      const response = await fetch(`/api/conversation/signed-url?agentId=${agentId}`);
      if (!response.ok) {
        throw new Error('Failed to get signed URL');
      }
      
      const { signed_url } = await response.json();
      const websocket = new WebSocket(signed_url);

      websocket.onopen = async () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        sendMessage(websocket, {
          type: "conversation_initiation_client_data",
        });
        await startStreaming();
        setIsListening(true);
      };

      websocket.onmessage = async (event) => {
        const data = JSON.parse(event.data) as ElevenLabsWebSocketEvent;

        // Handle ping events to keep connection alive
        if (data.type === "ping") {
          setTimeout(() => {
            sendMessage(websocket, {
              type: "pong",
              event_id: data.ping_event.event_id,
            });
          }, data.ping_event.ping_ms || 0);
        }

        if (data.type === "user_transcript") {
          const { user_transcription_event } = data;
          setUserTranscript(user_transcription_event.user_transcript);
          console.log("User transcript:", user_transcription_event.user_transcript);
        }

        if (data.type === "agent_response") {
          const { agent_response_event } = data;
          setAgentResponse(agent_response_event.agent_response);
          console.log("Agent response:", agent_response_event.agent_response);
        }

        if (data.type === "interruption") {
          console.log("Conversation interrupted");
          // Clear audio queue on interruption
          audioQueueRef.current = [];
          isPlayingRef.current = false;
        }

        if (data.type === "audio") {
          const { audio_event } = data;
          console.log('Received audio chunk, length:', audio_event.audio_base_64.length);

          // Debug: Check audio format by looking at base64 header
          const base64Sample = audio_event.audio_base_64.substring(0, 50);
          console.log('Audio base64 sample:', base64Sample);

          // Try to detect format from base64 header
          try {
            const binaryString = atob(audio_event.audio_base_64.substring(0, 20));
            const header = Array.from(binaryString).map(c => c.charCodeAt(0).toString(16).padStart(2, '0')).join(' ');
            console.log('Audio header bytes:', header);
          } catch (e) {
            console.log('Could not decode header');
          }

          // Buffer audio chunks instead of playing immediately
          bufferAudioChunk(audio_event.audio_base_64);
        }
      };

      websocketRef.current = websocket;

      websocket.onclose = async () => {
        console.log('WebSocket disconnected');
        websocketRef.current = null;
        setIsConnected(false);
        setIsListening(false);
        stopStreaming();
      };

      websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
      };

    } catch (error) {
      console.error('Failed to start conversation:', error);
      throw error;
    }
  }, [agentId, isConnected, startStreaming, initAudioContext, bufferAudioChunk]);

  const stopConversation = useCallback(async () => {
    if (!websocketRef.current) return;

    websocketRef.current.close();
    setUserTranscript('');
    setAgentResponse('');
    audioQueueRef.current = [];
    audioBufferRef.current = '';
    isPlayingRef.current = false;

    // Clear buffer timeout
    if (bufferTimeoutRef.current) {
      clearTimeout(bufferTimeoutRef.current);
      bufferTimeoutRef.current = null;
    }
  }, []);

  useEffect(() => {
    return () => {
      if (websocketRef.current) {
        websocketRef.current.close();
      }
    };
  }, []);

  return {
    startConversation,
    stopConversation,
    isConnected,
    isListening,
    userTranscript,
    agentResponse,
  };
};

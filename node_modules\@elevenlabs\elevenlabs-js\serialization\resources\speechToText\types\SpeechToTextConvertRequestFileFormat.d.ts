/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as ElevenLabs from "../../../../api/index";
import * as core from "../../../../core";
export declare const SpeechToTextConvertRequestFileFormat: core.serialization.Schema<serializers.SpeechToTextConvertRequestFileFormat.Raw, ElevenLabs.SpeechToTextConvertRequestFileFormat>;
export declare namespace SpeechToTextConvertRequestFileFormat {
    type Raw = "pcm_s16le_16" | "other";
}

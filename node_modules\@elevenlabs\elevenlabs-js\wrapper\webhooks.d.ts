import { Webhooks } from '../api/resources/webhooks/client/Client';
/**
 * Extended webhook client that includes both auto-generated API methods and custom functionality
 */
export declare class WebhooksClient extends Webhooks {
    /**
     * Constructs a webhook event object from a payload and signature.
     * Verifies the webhook signature to ensure the event came from Eleven<PERSON>abs.
     *
     * @param rawBody - The webhook request body. Must be the raw body, not a JSON object
     * @param sigHeader - The signature header from the request
     * @param secret - Your webhook secret
     * @returns The verified webhook event
     * @throws {ElevenLabsError} if the signature is invalid or missing
     */
    constructEvent(rawBody: string, sigHeader: string, secret: string): Promise<any>;
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[agentId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useAgentConversation.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useAgentConversation.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAgentConversation: () => (/* binding */ useAgentConversation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var voice_stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! voice-stream */ \"(app-pages-browser)/../node_modules/voice-stream/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAgentConversation auto */ \n\nconst sendMessage = (websocket, request)=>{\n    if (websocket.readyState !== WebSocket.OPEN) {\n        return;\n    }\n    websocket.send(JSON.stringify(request));\n};\nconst useAgentConversation = (agentId)=>{\n    const websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [userTranscript, setUserTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [agentResponse, setAgentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isPlayingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const audioBufferRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const bufferTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { startStreaming, stopStreaming } = (0,voice_stream__WEBPACK_IMPORTED_MODULE_1__.useVoiceStream)({\n        onAudioChunked: {\n            \"useAgentConversation.useVoiceStream\": (audioData)=>{\n                if (!websocketRef.current) return;\n                sendMessage(websocketRef.current, {\n                    user_audio_chunk: audioData\n                });\n            }\n        }[\"useAgentConversation.useVoiceStream\"]\n    });\n    // Initialize audio context\n    const initAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[initAudioContext]\": async ()=>{\n            if (!audioContextRef.current) {\n                try {\n                    const AudioContextClass = window.AudioContext || window.webkitAudioContext;\n                    audioContextRef.current = new AudioContextClass();\n                    // Resume audio context if it's suspended (required by some browsers)\n                    if (audioContextRef.current.state === 'suspended') {\n                        await audioContextRef.current.resume();\n                    }\n                } catch (error) {\n                    console.error('Failed to initialize audio context:', error);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[initAudioContext]\"], []);\n    // Play raw PCM audio from base64 using Web Audio API\n    const playAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[playAudio]\": async (base64Audio)=>{\n            try {\n                console.log('Attempting to play audio chunk, length:', base64Audio.length);\n                // Initialize audio context if needed\n                if (!audioContextRef.current) {\n                    await initAudioContext();\n                }\n                if (!audioContextRef.current) {\n                    throw new Error('Audio context not available');\n                }\n                // Convert base64 to ArrayBuffer\n                const binaryString = atob(base64Audio);\n                const arrayBuffer = new ArrayBuffer(binaryString.length);\n                const uint8Array = new Uint8Array(arrayBuffer);\n                for(let i = 0; i < binaryString.length; i++){\n                    uint8Array[i] = binaryString.charCodeAt(i);\n                }\n                console.log('Decoded audio buffer size:', arrayBuffer.byteLength);\n                // ElevenLabs likely sends raw PCM data, so we need to create AudioBuffer manually\n                try {\n                    // Assume 16-bit PCM, 22050 Hz sample rate (common for ElevenLabs)\n                    const sampleRate = 22050;\n                    const channels = 1; // mono\n                    const bytesPerSample = 2; // 16-bit\n                    const numSamples = arrayBuffer.byteLength / bytesPerSample;\n                    // Create AudioBuffer\n                    const audioBuffer = audioContextRef.current.createBuffer(channels, numSamples, sampleRate);\n                    const channelData = audioBuffer.getChannelData(0);\n                    // Convert 16-bit PCM to float32 (-1 to 1)\n                    const dataView = new DataView(arrayBuffer);\n                    for(let i = 0; i < numSamples; i++){\n                        const sample = dataView.getInt16(i * 2, true); // little-endian\n                        channelData[i] = sample / 32768.0; // Convert to -1 to 1 range\n                    }\n                    console.log('Created PCM audio buffer:', audioBuffer.duration, 'seconds');\n                    // Play the audio\n                    const source = audioContextRef.current.createBufferSource();\n                    source.buffer = audioBuffer;\n                    source.connect(audioContextRef.current.destination);\n                    return new Promise({\n                        \"useAgentConversation.useCallback[playAudio]\": (resolve)=>{\n                            source.onended = ({\n                                \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                    console.log('PCM audio playback completed');\n                                    resolve();\n                                }\n                            })[\"useAgentConversation.useCallback[playAudio]\"];\n                            source.start();\n                        }\n                    }[\"useAgentConversation.useCallback[playAudio]\"]);\n                } catch (pcmError) {\n                    console.log('PCM playback failed, trying different sample rates:', pcmError);\n                    // Try different sample rates\n                    const sampleRates = [\n                        24000,\n                        16000,\n                        44100,\n                        48000\n                    ];\n                    for (const sampleRate of sampleRates){\n                        try {\n                            const channels = 1;\n                            const bytesPerSample = 2;\n                            const numSamples = arrayBuffer.byteLength / bytesPerSample;\n                            const audioBuffer = audioContextRef.current.createBuffer(channels, numSamples, sampleRate);\n                            const channelData = audioBuffer.getChannelData(0);\n                            const dataView = new DataView(arrayBuffer);\n                            for(let i = 0; i < numSamples; i++){\n                                const sample = dataView.getInt16(i * 2, true);\n                                channelData[i] = sample / 32768.0;\n                            }\n                            const source = audioContextRef.current.createBufferSource();\n                            source.buffer = audioBuffer;\n                            source.connect(audioContextRef.current.destination);\n                            console.log(\"Playing with sample rate: \".concat(sampleRate, \"Hz\"));\n                            return new Promise({\n                                \"useAgentConversation.useCallback[playAudio]\": (resolve)=>{\n                                    source.onended = ({\n                                        \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                            console.log(\"Audio played successfully at \".concat(sampleRate, \"Hz\"));\n                                            resolve();\n                                        }\n                                    })[\"useAgentConversation.useCallback[playAudio]\"];\n                                    source.start();\n                                }\n                            }[\"useAgentConversation.useCallback[playAudio]\"]);\n                        } catch (rateError) {\n                            console.log(\"Sample rate \".concat(sampleRate, \"Hz failed, trying next...\"));\n                            continue;\n                        }\n                    }\n                    throw new Error('All sample rates failed');\n                }\n            } catch (error) {\n                console.error('Error playing audio:', error);\n                console.log('Base64 sample:', base64Audio.substring(0, 50));\n            }\n        }\n    }[\"useAgentConversation.useCallback[playAudio]\"], [\n        initAudioContext\n    ]);\n    // Process audio queue\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingRef.current || audioQueueRef.current.length === 0) return;\n            isPlayingRef.current = true;\n            const audioData = audioQueueRef.current.shift();\n            if (audioData) {\n                try {\n                    await playAudio(audioData);\n                } catch (error) {\n                    console.error('Error in processAudioQueue:', error);\n                // Continue processing even if one audio chunk fails\n                }\n            }\n            isPlayingRef.current = false;\n            // Process next audio in queue\n            if (audioQueueRef.current.length > 0) {\n                setTimeout({\n                    \"useAgentConversation.useCallback[processAudioQueue]\": ()=>processAudioQueue()\n                }[\"useAgentConversation.useCallback[processAudioQueue]\"], 50); // Small delay between chunks\n            }\n        }\n    }[\"useAgentConversation.useCallback[processAudioQueue]\"], [\n        playAudio\n    ]);\n    // Play audio chunks directly without complex buffering\n    const handleAudioChunk = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[handleAudioChunk]\": (base64Audio)=>{\n            console.log('Adding audio chunk to queue, length:', base64Audio.length);\n            // Add to queue for sequential playback\n            audioQueueRef.current.push(base64Audio);\n            // Start processing queue if not already playing\n            if (!isPlayingRef.current) {\n                processAudioQueue();\n            }\n        }\n    }[\"useAgentConversation.useCallback[handleAudioChunk]\"], [\n        processAudioQueue\n    ]);\n    const startConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n            if (isConnected) return;\n            try {\n                // Request microphone permission\n                await navigator.mediaDevices.getUserMedia({\n                    audio: true\n                });\n                // Initialize audio context for playback\n                await initAudioContext();\n                // Get signed URL for WebSocket connection\n                const response = await fetch(\"/api/conversation/signed-url?agentId=\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to get signed URL');\n                }\n                const { signed_url } = await response.json();\n                const websocket = new WebSocket(signed_url);\n                websocket.onopen = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        sendMessage(websocket, {\n                            type: \"conversation_initiation_client_data\"\n                        });\n                        await startStreaming();\n                        setIsListening(true);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onmessage = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async (event)=>{\n                        const data = JSON.parse(event.data);\n                        // Handle ping events to keep connection alive\n                        if (data.type === \"ping\") {\n                            setTimeout({\n                                \"useAgentConversation.useCallback[startConversation]\": ()=>{\n                                    sendMessage(websocket, {\n                                        type: \"pong\",\n                                        event_id: data.ping_event.event_id\n                                    });\n                                }\n                            }[\"useAgentConversation.useCallback[startConversation]\"], data.ping_event.ping_ms || 0);\n                        }\n                        if (data.type === \"user_transcript\") {\n                            const { user_transcription_event } = data;\n                            setUserTranscript(user_transcription_event.user_transcript);\n                            console.log(\"User transcript:\", user_transcription_event.user_transcript);\n                        }\n                        if (data.type === \"agent_response\") {\n                            const { agent_response_event } = data;\n                            setAgentResponse(agent_response_event.agent_response);\n                            console.log(\"Agent response:\", agent_response_event.agent_response);\n                        }\n                        if (data.type === \"interruption\") {\n                            console.log(\"Conversation interrupted\");\n                            // Clear audio queue on interruption\n                            audioQueueRef.current = [];\n                            isPlayingRef.current = false;\n                        }\n                        if (data.type === \"audio\") {\n                            const { audio_event } = data;\n                            console.log('Received audio chunk, length:', audio_event.audio_base_64.length);\n                            // Debug: Check audio format by looking at base64 header\n                            const base64Sample = audio_event.audio_base_64.substring(0, 50);\n                            console.log('Audio base64 sample:', base64Sample);\n                            // Try to detect format from base64 header\n                            try {\n                                const binaryString = atob(audio_event.audio_base_64.substring(0, 20));\n                                const header = Array.from(binaryString).map({\n                                    \"useAgentConversation.useCallback[startConversation].header\": (c)=>c.charCodeAt(0).toString(16).padStart(2, '0')\n                                }[\"useAgentConversation.useCallback[startConversation].header\"]).join(' ');\n                                console.log('Audio header bytes:', header);\n                            } catch (e) {\n                                console.log('Could not decode header');\n                            }\n                            // Handle audio chunk directly\n                            handleAudioChunk(audio_event.audio_base_64);\n                        }\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocketRef.current = websocket;\n                websocket.onclose = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket disconnected');\n                        websocketRef.current = null;\n                        setIsConnected(false);\n                        setIsListening(false);\n                        stopStreaming();\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onerror = ({\n                    \"useAgentConversation.useCallback[startConversation]\": (error)=>{\n                        console.error('WebSocket error:', error);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                throw error;\n            }\n        }\n    }[\"useAgentConversation.useCallback[startConversation]\"], [\n        agentId,\n        isConnected,\n        startStreaming,\n        initAudioContext,\n        bufferAudioChunk\n    ]);\n    const stopConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[stopConversation]\": async ()=>{\n            if (!websocketRef.current) return;\n            websocketRef.current.close();\n            setUserTranscript('');\n            setAgentResponse('');\n            audioQueueRef.current = [];\n            audioBufferRef.current = [];\n            isPlayingRef.current = false;\n            // Clear buffer timeout\n            if (bufferTimeoutRef.current) {\n                clearTimeout(bufferTimeoutRef.current);\n                bufferTimeoutRef.current = null;\n            }\n        }\n    }[\"useAgentConversation.useCallback[stopConversation]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAgentConversation.useEffect\": ()=>{\n            return ({\n                \"useAgentConversation.useEffect\": ()=>{\n                    if (websocketRef.current) {\n                        websocketRef.current.close();\n                    }\n                }\n            })[\"useAgentConversation.useEffect\"];\n        }\n    }[\"useAgentConversation.useEffect\"], []);\n    return {\n        startConversation,\n        stopConversation,\n        isConnected,\n        isListening,\n        userTranscript,\n        agentResponse\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VBZ2VudENvbnZlcnNhdGlvbi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OzBFQUVpRTtBQUNuQjtBQUc5QyxNQUFNSyxjQUFjLENBQUNDLFdBQXNCQztJQUN6QyxJQUFJRCxVQUFVRSxVQUFVLEtBQUtDLFVBQVVDLElBQUksRUFBRTtRQUMzQztJQUNGO0lBQ0FKLFVBQVVLLElBQUksQ0FBQ0MsS0FBS0MsU0FBUyxDQUFDTjtBQUNoQztBQUVPLE1BQU1PLHVCQUF1QixDQUFDQztJQUNuQyxNQUFNQyxlQUFlZCw2Q0FBTUEsQ0FBbUI7SUFDOUMsTUFBTSxDQUFDZSxhQUFhQyxlQUFlLEdBQUdmLCtDQUFRQSxDQUFVO0lBQ3hELE1BQU0sQ0FBQ2dCLGdCQUFnQkMsa0JBQWtCLEdBQUdqQiwrQ0FBUUEsQ0FBUztJQUM3RCxNQUFNLENBQUNrQixlQUFlQyxpQkFBaUIsR0FBR25CLCtDQUFRQSxDQUFTO0lBQzNELE1BQU0sQ0FBQ29CLGFBQWFDLGVBQWUsR0FBR3JCLCtDQUFRQSxDQUFVO0lBQ3hELE1BQU1zQixrQkFBa0J2Qiw2Q0FBTUEsQ0FBc0I7SUFDcEQsTUFBTXdCLGdCQUFnQnhCLDZDQUFNQSxDQUFXLEVBQUU7SUFDekMsTUFBTXlCLGVBQWV6Qiw2Q0FBTUEsQ0FBVTtJQUNyQyxNQUFNMEIsaUJBQWlCMUIsNkNBQU1BLENBQWUsRUFBRTtJQUM5QyxNQUFNMkIsbUJBQW1CM0IsNkNBQU1BLENBQXdCO0lBRXZELE1BQU0sRUFBRTRCLGNBQWMsRUFBRUMsYUFBYSxFQUFFLEdBQUczQiw0REFBY0EsQ0FBQztRQUN2RDRCLGNBQWM7bURBQUUsQ0FBQ0M7Z0JBQ2YsSUFBSSxDQUFDakIsYUFBYWtCLE9BQU8sRUFBRTtnQkFDM0I3QixZQUFZVyxhQUFha0IsT0FBTyxFQUFFO29CQUNoQ0Msa0JBQWtCRjtnQkFDcEI7WUFDRjs7SUFDRjtJQUVBLDJCQUEyQjtJQUMzQixNQUFNRyxtQkFBbUJwQyxrREFBV0E7OERBQUM7WUFDbkMsSUFBSSxDQUFDeUIsZ0JBQWdCUyxPQUFPLEVBQUU7Z0JBQzVCLElBQUk7b0JBQ0YsTUFBTUcsb0JBQW9CQyxPQUFPQyxZQUFZLElBQUksT0FBZ0JDLGtCQUFrQjtvQkFDbkZmLGdCQUFnQlMsT0FBTyxHQUFHLElBQUlHO29CQUU5QixxRUFBcUU7b0JBQ3JFLElBQUlaLGdCQUFnQlMsT0FBTyxDQUFDTyxLQUFLLEtBQUssYUFBYTt3QkFDakQsTUFBTWhCLGdCQUFnQlMsT0FBTyxDQUFDUSxNQUFNO29CQUN0QztnQkFDRixFQUFFLE9BQU9DLE9BQU87b0JBQ2RDLFFBQVFELEtBQUssQ0FBQyx1Q0FBdUNBO2dCQUN2RDtZQUNGO1FBQ0Y7NkRBQUcsRUFBRTtJQUVMLHFEQUFxRDtJQUNyRCxNQUFNRSxZQUFZN0Msa0RBQVdBO3VEQUFDLE9BQU84QztZQUNuQyxJQUFJO2dCQUNGRixRQUFRRyxHQUFHLENBQUMsMkNBQTJDRCxZQUFZRSxNQUFNO2dCQUV6RSxxQ0FBcUM7Z0JBQ3JDLElBQUksQ0FBQ3ZCLGdCQUFnQlMsT0FBTyxFQUFFO29CQUM1QixNQUFNRTtnQkFDUjtnQkFFQSxJQUFJLENBQUNYLGdCQUFnQlMsT0FBTyxFQUFFO29CQUM1QixNQUFNLElBQUllLE1BQU07Z0JBQ2xCO2dCQUVBLGdDQUFnQztnQkFDaEMsTUFBTUMsZUFBZUMsS0FBS0w7Z0JBQzFCLE1BQU1NLGNBQWMsSUFBSUMsWUFBWUgsYUFBYUYsTUFBTTtnQkFDdkQsTUFBTU0sYUFBYSxJQUFJQyxXQUFXSDtnQkFFbEMsSUFBSyxJQUFJSSxJQUFJLEdBQUdBLElBQUlOLGFBQWFGLE1BQU0sRUFBRVEsSUFBSztvQkFDNUNGLFVBQVUsQ0FBQ0UsRUFBRSxHQUFHTixhQUFhTyxVQUFVLENBQUNEO2dCQUMxQztnQkFFQVosUUFBUUcsR0FBRyxDQUFDLDhCQUE4QkssWUFBWU0sVUFBVTtnQkFFaEUsa0ZBQWtGO2dCQUNsRixJQUFJO29CQUNGLGtFQUFrRTtvQkFDbEUsTUFBTUMsYUFBYTtvQkFDbkIsTUFBTUMsV0FBVyxHQUFHLE9BQU87b0JBQzNCLE1BQU1DLGlCQUFpQixHQUFHLFNBQVM7b0JBQ25DLE1BQU1DLGFBQWFWLFlBQVlNLFVBQVUsR0FBR0c7b0JBRTVDLHFCQUFxQjtvQkFDckIsTUFBTUUsY0FBY3RDLGdCQUFnQlMsT0FBTyxDQUFDOEIsWUFBWSxDQUFDSixVQUFVRSxZQUFZSDtvQkFDL0UsTUFBTU0sY0FBY0YsWUFBWUcsY0FBYyxDQUFDO29CQUUvQywwQ0FBMEM7b0JBQzFDLE1BQU1DLFdBQVcsSUFBSUMsU0FBU2hCO29CQUM5QixJQUFLLElBQUlJLElBQUksR0FBR0EsSUFBSU0sWUFBWU4sSUFBSzt3QkFDbkMsTUFBTWEsU0FBU0YsU0FBU0csUUFBUSxDQUFDZCxJQUFJLEdBQUcsT0FBTyxnQkFBZ0I7d0JBQy9EUyxXQUFXLENBQUNULEVBQUUsR0FBR2EsU0FBUyxTQUFTLDJCQUEyQjtvQkFDaEU7b0JBRUF6QixRQUFRRyxHQUFHLENBQUMsNkJBQTZCZ0IsWUFBWVEsUUFBUSxFQUFFO29CQUUvRCxpQkFBaUI7b0JBQ2pCLE1BQU1DLFNBQVMvQyxnQkFBZ0JTLE9BQU8sQ0FBQ3VDLGtCQUFrQjtvQkFDekRELE9BQU9FLE1BQU0sR0FBR1g7b0JBQ2hCUyxPQUFPRyxPQUFPLENBQUNsRCxnQkFBZ0JTLE9BQU8sQ0FBQzBDLFdBQVc7b0JBRWxELE9BQU8sSUFBSUM7dUVBQWMsQ0FBQ0M7NEJBQ3hCTixPQUFPTyxPQUFPOytFQUFHO29DQUNmbkMsUUFBUUcsR0FBRyxDQUFDO29DQUNaK0I7Z0NBQ0Y7OzRCQUNBTixPQUFPUSxLQUFLO3dCQUNkOztnQkFFRixFQUFFLE9BQU9DLFVBQVU7b0JBQ2pCckMsUUFBUUcsR0FBRyxDQUFDLHVEQUF1RGtDO29CQUVuRSw2QkFBNkI7b0JBQzdCLE1BQU1DLGNBQWM7d0JBQUM7d0JBQU87d0JBQU87d0JBQU87cUJBQU07b0JBRWhELEtBQUssTUFBTXZCLGNBQWN1QixZQUFhO3dCQUNwQyxJQUFJOzRCQUNGLE1BQU10QixXQUFXOzRCQUNqQixNQUFNQyxpQkFBaUI7NEJBQ3ZCLE1BQU1DLGFBQWFWLFlBQVlNLFVBQVUsR0FBR0c7NEJBRTVDLE1BQU1FLGNBQWN0QyxnQkFBZ0JTLE9BQU8sQ0FBQzhCLFlBQVksQ0FBQ0osVUFBVUUsWUFBWUg7NEJBQy9FLE1BQU1NLGNBQWNGLFlBQVlHLGNBQWMsQ0FBQzs0QkFFL0MsTUFBTUMsV0FBVyxJQUFJQyxTQUFTaEI7NEJBQzlCLElBQUssSUFBSUksSUFBSSxHQUFHQSxJQUFJTSxZQUFZTixJQUFLO2dDQUNuQyxNQUFNYSxTQUFTRixTQUFTRyxRQUFRLENBQUNkLElBQUksR0FBRztnQ0FDeENTLFdBQVcsQ0FBQ1QsRUFBRSxHQUFHYSxTQUFTOzRCQUM1Qjs0QkFFQSxNQUFNRyxTQUFTL0MsZ0JBQWdCUyxPQUFPLENBQUN1QyxrQkFBa0I7NEJBQ3pERCxPQUFPRSxNQUFNLEdBQUdYOzRCQUNoQlMsT0FBT0csT0FBTyxDQUFDbEQsZ0JBQWdCUyxPQUFPLENBQUMwQyxXQUFXOzRCQUVsRGhDLFFBQVFHLEdBQUcsQ0FBQyw2QkFBd0MsT0FBWFksWUFBVzs0QkFFcEQsT0FBTyxJQUFJa0I7K0VBQWMsQ0FBQ0M7b0NBQ3hCTixPQUFPTyxPQUFPO3VGQUFHOzRDQUNmbkMsUUFBUUcsR0FBRyxDQUFDLGdDQUEyQyxPQUFYWSxZQUFXOzRDQUN2RG1CO3dDQUNGOztvQ0FDQU4sT0FBT1EsS0FBSztnQ0FDZDs7d0JBRUYsRUFBRSxPQUFPRyxXQUFXOzRCQUNsQnZDLFFBQVFHLEdBQUcsQ0FBQyxlQUEwQixPQUFYWSxZQUFXOzRCQUN0Qzt3QkFDRjtvQkFDRjtvQkFFQSxNQUFNLElBQUlWLE1BQU07Z0JBQ2xCO1lBRUYsRUFBRSxPQUFPTixPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsd0JBQXdCQTtnQkFDdENDLFFBQVFHLEdBQUcsQ0FBQyxrQkFBa0JELFlBQVlzQyxTQUFTLENBQUMsR0FBRztZQUN6RDtRQUNGO3NEQUFHO1FBQUNoRDtLQUFpQjtJQUVyQixzQkFBc0I7SUFDdEIsTUFBTWlELG9CQUFvQnJGLGtEQUFXQTsrREFBQztZQUNwQyxJQUFJMkIsYUFBYU8sT0FBTyxJQUFJUixjQUFjUSxPQUFPLENBQUNjLE1BQU0sS0FBSyxHQUFHO1lBRWhFckIsYUFBYU8sT0FBTyxHQUFHO1lBQ3ZCLE1BQU1ELFlBQVlQLGNBQWNRLE9BQU8sQ0FBQ29ELEtBQUs7WUFFN0MsSUFBSXJELFdBQVc7Z0JBQ2IsSUFBSTtvQkFDRixNQUFNWSxVQUFVWjtnQkFDbEIsRUFBRSxPQUFPVSxPQUFPO29CQUNkQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCQTtnQkFDN0Msb0RBQW9EO2dCQUN0RDtZQUNGO1lBRUFoQixhQUFhTyxPQUFPLEdBQUc7WUFFdkIsOEJBQThCO1lBQzlCLElBQUlSLGNBQWNRLE9BQU8sQ0FBQ2MsTUFBTSxHQUFHLEdBQUc7Z0JBQ3BDdUM7MkVBQVcsSUFBTUY7MEVBQXFCLEtBQUssNkJBQTZCO1lBQzFFO1FBQ0Y7OERBQUc7UUFBQ3hDO0tBQVU7SUFFZCx1REFBdUQ7SUFDdkQsTUFBTTJDLG1CQUFtQnhGLGtEQUFXQTs4REFBQyxDQUFDOEM7WUFDcENGLFFBQVFHLEdBQUcsQ0FBQyx3Q0FBd0NELFlBQVlFLE1BQU07WUFFdEUsdUNBQXVDO1lBQ3ZDdEIsY0FBY1EsT0FBTyxDQUFDdUQsSUFBSSxDQUFDM0M7WUFFM0IsZ0RBQWdEO1lBQ2hELElBQUksQ0FBQ25CLGFBQWFPLE9BQU8sRUFBRTtnQkFDekJtRDtZQUNGO1FBQ0Y7NkRBQUc7UUFBQ0E7S0FBa0I7SUFFdEIsTUFBTUssb0JBQW9CMUYsa0RBQVdBOytEQUFDO1lBQ3BDLElBQUlpQixhQUFhO1lBRWpCLElBQUk7Z0JBQ0YsZ0NBQWdDO2dCQUNoQyxNQUFNMEUsVUFBVUMsWUFBWSxDQUFDQyxZQUFZLENBQUM7b0JBQUVDLE9BQU87Z0JBQUs7Z0JBRXhELHdDQUF3QztnQkFDeEMsTUFBTTFEO2dCQUVOLDBDQUEwQztnQkFDMUMsTUFBTTJELFdBQVcsTUFBTUMsTUFBTSx3Q0FBZ0QsT0FBUmpGO2dCQUNyRSxJQUFJLENBQUNnRixTQUFTRSxFQUFFLEVBQUU7b0JBQ2hCLE1BQU0sSUFBSWhELE1BQU07Z0JBQ2xCO2dCQUVBLE1BQU0sRUFBRWlELFVBQVUsRUFBRSxHQUFHLE1BQU1ILFNBQVNJLElBQUk7Z0JBQzFDLE1BQU03RixZQUFZLElBQUlHLFVBQVV5RjtnQkFFaEM1RixVQUFVOEYsTUFBTTsyRUFBRzt3QkFDakJ4RCxRQUFRRyxHQUFHLENBQUM7d0JBQ1o3QixlQUFlO3dCQUNmYixZQUFZQyxXQUFXOzRCQUNyQitGLE1BQU07d0JBQ1I7d0JBQ0EsTUFBTXZFO3dCQUNOTixlQUFlO29CQUNqQjs7Z0JBRUFsQixVQUFVZ0csU0FBUzsyRUFBRyxPQUFPQzt3QkFDM0IsTUFBTUMsT0FBTzVGLEtBQUs2RixLQUFLLENBQUNGLE1BQU1DLElBQUk7d0JBRWxDLDhDQUE4Qzt3QkFDOUMsSUFBSUEsS0FBS0gsSUFBSSxLQUFLLFFBQVE7NEJBQ3hCZDt1RkFBVztvQ0FDVGxGLFlBQVlDLFdBQVc7d0NBQ3JCK0YsTUFBTTt3Q0FDTkssVUFBVUYsS0FBS0csVUFBVSxDQUFDRCxRQUFRO29DQUNwQztnQ0FDRjtzRkFBR0YsS0FBS0csVUFBVSxDQUFDQyxPQUFPLElBQUk7d0JBQ2hDO3dCQUVBLElBQUlKLEtBQUtILElBQUksS0FBSyxtQkFBbUI7NEJBQ25DLE1BQU0sRUFBRVEsd0JBQXdCLEVBQUUsR0FBR0w7NEJBQ3JDcEYsa0JBQWtCeUYseUJBQXlCQyxlQUFlOzRCQUMxRGxFLFFBQVFHLEdBQUcsQ0FBQyxvQkFBb0I4RCx5QkFBeUJDLGVBQWU7d0JBQzFFO3dCQUVBLElBQUlOLEtBQUtILElBQUksS0FBSyxrQkFBa0I7NEJBQ2xDLE1BQU0sRUFBRVUsb0JBQW9CLEVBQUUsR0FBR1A7NEJBQ2pDbEYsaUJBQWlCeUYscUJBQXFCQyxjQUFjOzRCQUNwRHBFLFFBQVFHLEdBQUcsQ0FBQyxtQkFBbUJnRSxxQkFBcUJDLGNBQWM7d0JBQ3BFO3dCQUVBLElBQUlSLEtBQUtILElBQUksS0FBSyxnQkFBZ0I7NEJBQ2hDekQsUUFBUUcsR0FBRyxDQUFDOzRCQUNaLG9DQUFvQzs0QkFDcENyQixjQUFjUSxPQUFPLEdBQUcsRUFBRTs0QkFDMUJQLGFBQWFPLE9BQU8sR0FBRzt3QkFDekI7d0JBRUEsSUFBSXNFLEtBQUtILElBQUksS0FBSyxTQUFTOzRCQUN6QixNQUFNLEVBQUVZLFdBQVcsRUFBRSxHQUFHVDs0QkFDeEI1RCxRQUFRRyxHQUFHLENBQUMsaUNBQWlDa0UsWUFBWUMsYUFBYSxDQUFDbEUsTUFBTTs0QkFFN0Usd0RBQXdEOzRCQUN4RCxNQUFNbUUsZUFBZUYsWUFBWUMsYUFBYSxDQUFDOUIsU0FBUyxDQUFDLEdBQUc7NEJBQzVEeEMsUUFBUUcsR0FBRyxDQUFDLHdCQUF3Qm9FOzRCQUVwQywwQ0FBMEM7NEJBQzFDLElBQUk7Z0NBQ0YsTUFBTWpFLGVBQWVDLEtBQUs4RCxZQUFZQyxhQUFhLENBQUM5QixTQUFTLENBQUMsR0FBRztnQ0FDakUsTUFBTWdDLFNBQVNDLE1BQU1DLElBQUksQ0FBQ3BFLGNBQWNxRSxHQUFHO2tHQUFDQyxDQUFBQSxJQUFLQSxFQUFFL0QsVUFBVSxDQUFDLEdBQUdnRSxRQUFRLENBQUMsSUFBSUMsUUFBUSxDQUFDLEdBQUc7aUdBQU1DLElBQUksQ0FBQztnQ0FDckcvRSxRQUFRRyxHQUFHLENBQUMsdUJBQXVCcUU7NEJBQ3JDLEVBQUUsT0FBT1EsR0FBRztnQ0FDVmhGLFFBQVFHLEdBQUcsQ0FBQzs0QkFDZDs0QkFFQSw4QkFBOEI7NEJBQzlCeUMsaUJBQWlCeUIsWUFBWUMsYUFBYTt3QkFDNUM7b0JBQ0Y7O2dCQUVBbEcsYUFBYWtCLE9BQU8sR0FBRzVCO2dCQUV2QkEsVUFBVXVILE9BQU87MkVBQUc7d0JBQ2xCakYsUUFBUUcsR0FBRyxDQUFDO3dCQUNaL0IsYUFBYWtCLE9BQU8sR0FBRzt3QkFDdkJoQixlQUFlO3dCQUNmTSxlQUFlO3dCQUNmTztvQkFDRjs7Z0JBRUF6QixVQUFVd0gsT0FBTzsyRUFBRyxDQUFDbkY7d0JBQ25CQyxRQUFRRCxLQUFLLENBQUMsb0JBQW9CQTtvQkFDcEM7O1lBRUYsRUFBRSxPQUFPQSxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtnQkFDL0MsTUFBTUE7WUFDUjtRQUNGOzhEQUFHO1FBQUM1QjtRQUFTRTtRQUFhYTtRQUFnQk07UUFBa0IyRjtLQUFpQjtJQUU3RSxNQUFNQyxtQkFBbUJoSSxrREFBV0E7OERBQUM7WUFDbkMsSUFBSSxDQUFDZ0IsYUFBYWtCLE9BQU8sRUFBRTtZQUUzQmxCLGFBQWFrQixPQUFPLENBQUMrRixLQUFLO1lBQzFCN0csa0JBQWtCO1lBQ2xCRSxpQkFBaUI7WUFDakJJLGNBQWNRLE9BQU8sR0FBRyxFQUFFO1lBQzFCTixlQUFlTSxPQUFPLEdBQUcsRUFBRTtZQUMzQlAsYUFBYU8sT0FBTyxHQUFHO1lBRXZCLHVCQUF1QjtZQUN2QixJQUFJTCxpQkFBaUJLLE9BQU8sRUFBRTtnQkFDNUJnRyxhQUFhckcsaUJBQWlCSyxPQUFPO2dCQUNyQ0wsaUJBQWlCSyxPQUFPLEdBQUc7WUFDN0I7UUFDRjs2REFBRyxFQUFFO0lBRUxqQyxnREFBU0E7MENBQUM7WUFDUjtrREFBTztvQkFDTCxJQUFJZSxhQUFha0IsT0FBTyxFQUFFO3dCQUN4QmxCLGFBQWFrQixPQUFPLENBQUMrRixLQUFLO29CQUM1QjtnQkFDRjs7UUFDRjt5Q0FBRyxFQUFFO0lBRUwsT0FBTztRQUNMdkM7UUFDQXNDO1FBQ0EvRztRQUNBTTtRQUNBSjtRQUNBRTtJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsiRTpcXHByb3RvdHlwZSBtYXMgbXVqaVxccHJvdG90eXBlXFxzcmNcXGhvb2tzXFx1c2VBZ2VudENvbnZlcnNhdGlvbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZUNhbGxiYWNrLCB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VWb2ljZVN0cmVhbSB9IGZyb20gJ3ZvaWNlLXN0cmVhbSc7XG5pbXBvcnQgdHlwZSB7IEVsZXZlbkxhYnNXZWJTb2NrZXRFdmVudCB9IGZyb20gJy4uL3R5cGVzL3dlYnNvY2tldCc7XG5cbmNvbnN0IHNlbmRNZXNzYWdlID0gKHdlYnNvY2tldDogV2ViU29ja2V0LCByZXF1ZXN0OiBvYmplY3QpID0+IHtcbiAgaWYgKHdlYnNvY2tldC5yZWFkeVN0YXRlICE9PSBXZWJTb2NrZXQuT1BFTikge1xuICAgIHJldHVybjtcbiAgfVxuICB3ZWJzb2NrZXQuc2VuZChKU09OLnN0cmluZ2lmeShyZXF1ZXN0KSk7XG59O1xuXG5leHBvcnQgY29uc3QgdXNlQWdlbnRDb252ZXJzYXRpb24gPSAoYWdlbnRJZDogc3RyaW5nKSA9PiB7XG4gIGNvbnN0IHdlYnNvY2tldFJlZiA9IHVzZVJlZjxXZWJTb2NrZXQgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2lzQ29ubmVjdGVkLCBzZXRJc0Nvbm5lY3RlZF0gPSB1c2VTdGF0ZTxib29sZWFuPihmYWxzZSk7XG4gIGNvbnN0IFt1c2VyVHJhbnNjcmlwdCwgc2V0VXNlclRyYW5zY3JpcHRdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG4gIGNvbnN0IFthZ2VudFJlc3BvbnNlLCBzZXRBZ2VudFJlc3BvbnNlXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuICBjb25zdCBbaXNMaXN0ZW5pbmcsIHNldElzTGlzdGVuaW5nXSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKTtcbiAgY29uc3QgYXVkaW9Db250ZXh0UmVmID0gdXNlUmVmPEF1ZGlvQ29udGV4dCB8IG51bGw+KG51bGwpO1xuICBjb25zdCBhdWRpb1F1ZXVlUmVmID0gdXNlUmVmPHN0cmluZ1tdPihbXSk7XG4gIGNvbnN0IGlzUGxheWluZ1JlZiA9IHVzZVJlZjxib29sZWFuPihmYWxzZSk7XG4gIGNvbnN0IGF1ZGlvQnVmZmVyUmVmID0gdXNlUmVmPFVpbnQ4QXJyYXlbXT4oW10pO1xuICBjb25zdCBidWZmZXJUaW1lb3V0UmVmID0gdXNlUmVmPE5vZGVKUy5UaW1lb3V0IHwgbnVsbD4obnVsbCk7XG5cbiAgY29uc3QgeyBzdGFydFN0cmVhbWluZywgc3RvcFN0cmVhbWluZyB9ID0gdXNlVm9pY2VTdHJlYW0oe1xuICAgIG9uQXVkaW9DaHVua2VkOiAoYXVkaW9EYXRhKSA9PiB7XG4gICAgICBpZiAoIXdlYnNvY2tldFJlZi5jdXJyZW50KSByZXR1cm47XG4gICAgICBzZW5kTWVzc2FnZSh3ZWJzb2NrZXRSZWYuY3VycmVudCwge1xuICAgICAgICB1c2VyX2F1ZGlvX2NodW5rOiBhdWRpb0RhdGEsXG4gICAgICB9KTtcbiAgICB9LFxuICB9KTtcblxuICAvLyBJbml0aWFsaXplIGF1ZGlvIGNvbnRleHRcbiAgY29uc3QgaW5pdEF1ZGlvQ29udGV4dCA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcbiAgICBpZiAoIWF1ZGlvQ29udGV4dFJlZi5jdXJyZW50KSB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBBdWRpb0NvbnRleHRDbGFzcyA9IHdpbmRvdy5BdWRpb0NvbnRleHQgfHwgKHdpbmRvdyBhcyBhbnkpLndlYmtpdEF1ZGlvQ29udGV4dDtcbiAgICAgICAgYXVkaW9Db250ZXh0UmVmLmN1cnJlbnQgPSBuZXcgQXVkaW9Db250ZXh0Q2xhc3MoKTtcblxuICAgICAgICAvLyBSZXN1bWUgYXVkaW8gY29udGV4dCBpZiBpdCdzIHN1c3BlbmRlZCAocmVxdWlyZWQgYnkgc29tZSBicm93c2VycylcbiAgICAgICAgaWYgKGF1ZGlvQ29udGV4dFJlZi5jdXJyZW50LnN0YXRlID09PSAnc3VzcGVuZGVkJykge1xuICAgICAgICAgIGF3YWl0IGF1ZGlvQ29udGV4dFJlZi5jdXJyZW50LnJlc3VtZSgpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gaW5pdGlhbGl6ZSBhdWRpbyBjb250ZXh0OicsIGVycm9yKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFtdKTtcblxuICAvLyBQbGF5IHJhdyBQQ00gYXVkaW8gZnJvbSBiYXNlNjQgdXNpbmcgV2ViIEF1ZGlvIEFQSVxuICBjb25zdCBwbGF5QXVkaW8gPSB1c2VDYWxsYmFjayhhc3luYyAoYmFzZTY0QXVkaW86IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygnQXR0ZW1wdGluZyB0byBwbGF5IGF1ZGlvIGNodW5rLCBsZW5ndGg6JywgYmFzZTY0QXVkaW8ubGVuZ3RoKTtcblxuICAgICAgLy8gSW5pdGlhbGl6ZSBhdWRpbyBjb250ZXh0IGlmIG5lZWRlZFxuICAgICAgaWYgKCFhdWRpb0NvbnRleHRSZWYuY3VycmVudCkge1xuICAgICAgICBhd2FpdCBpbml0QXVkaW9Db250ZXh0KCk7XG4gICAgICB9XG5cbiAgICAgIGlmICghYXVkaW9Db250ZXh0UmVmLmN1cnJlbnQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdBdWRpbyBjb250ZXh0IG5vdCBhdmFpbGFibGUnKTtcbiAgICAgIH1cblxuICAgICAgLy8gQ29udmVydCBiYXNlNjQgdG8gQXJyYXlCdWZmZXJcbiAgICAgIGNvbnN0IGJpbmFyeVN0cmluZyA9IGF0b2IoYmFzZTY0QXVkaW8pO1xuICAgICAgY29uc3QgYXJyYXlCdWZmZXIgPSBuZXcgQXJyYXlCdWZmZXIoYmluYXJ5U3RyaW5nLmxlbmd0aCk7XG4gICAgICBjb25zdCB1aW50OEFycmF5ID0gbmV3IFVpbnQ4QXJyYXkoYXJyYXlCdWZmZXIpO1xuXG4gICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGJpbmFyeVN0cmluZy5sZW5ndGg7IGkrKykge1xuICAgICAgICB1aW50OEFycmF5W2ldID0gYmluYXJ5U3RyaW5nLmNoYXJDb2RlQXQoaSk7XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCdEZWNvZGVkIGF1ZGlvIGJ1ZmZlciBzaXplOicsIGFycmF5QnVmZmVyLmJ5dGVMZW5ndGgpO1xuXG4gICAgICAvLyBFbGV2ZW5MYWJzIGxpa2VseSBzZW5kcyByYXcgUENNIGRhdGEsIHNvIHdlIG5lZWQgdG8gY3JlYXRlIEF1ZGlvQnVmZmVyIG1hbnVhbGx5XG4gICAgICB0cnkge1xuICAgICAgICAvLyBBc3N1bWUgMTYtYml0IFBDTSwgMjIwNTAgSHogc2FtcGxlIHJhdGUgKGNvbW1vbiBmb3IgRWxldmVuTGFicylcbiAgICAgICAgY29uc3Qgc2FtcGxlUmF0ZSA9IDIyMDUwO1xuICAgICAgICBjb25zdCBjaGFubmVscyA9IDE7IC8vIG1vbm9cbiAgICAgICAgY29uc3QgYnl0ZXNQZXJTYW1wbGUgPSAyOyAvLyAxNi1iaXRcbiAgICAgICAgY29uc3QgbnVtU2FtcGxlcyA9IGFycmF5QnVmZmVyLmJ5dGVMZW5ndGggLyBieXRlc1BlclNhbXBsZTtcblxuICAgICAgICAvLyBDcmVhdGUgQXVkaW9CdWZmZXJcbiAgICAgICAgY29uc3QgYXVkaW9CdWZmZXIgPSBhdWRpb0NvbnRleHRSZWYuY3VycmVudC5jcmVhdGVCdWZmZXIoY2hhbm5lbHMsIG51bVNhbXBsZXMsIHNhbXBsZVJhdGUpO1xuICAgICAgICBjb25zdCBjaGFubmVsRGF0YSA9IGF1ZGlvQnVmZmVyLmdldENoYW5uZWxEYXRhKDApO1xuXG4gICAgICAgIC8vIENvbnZlcnQgMTYtYml0IFBDTSB0byBmbG9hdDMyICgtMSB0byAxKVxuICAgICAgICBjb25zdCBkYXRhVmlldyA9IG5ldyBEYXRhVmlldyhhcnJheUJ1ZmZlcik7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbnVtU2FtcGxlczsgaSsrKSB7XG4gICAgICAgICAgY29uc3Qgc2FtcGxlID0gZGF0YVZpZXcuZ2V0SW50MTYoaSAqIDIsIHRydWUpOyAvLyBsaXR0bGUtZW5kaWFuXG4gICAgICAgICAgY2hhbm5lbERhdGFbaV0gPSBzYW1wbGUgLyAzMjc2OC4wOyAvLyBDb252ZXJ0IHRvIC0xIHRvIDEgcmFuZ2VcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnNvbGUubG9nKCdDcmVhdGVkIFBDTSBhdWRpbyBidWZmZXI6JywgYXVkaW9CdWZmZXIuZHVyYXRpb24sICdzZWNvbmRzJyk7XG5cbiAgICAgICAgLy8gUGxheSB0aGUgYXVkaW9cbiAgICAgICAgY29uc3Qgc291cmNlID0gYXVkaW9Db250ZXh0UmVmLmN1cnJlbnQuY3JlYXRlQnVmZmVyU291cmNlKCk7XG4gICAgICAgIHNvdXJjZS5idWZmZXIgPSBhdWRpb0J1ZmZlcjtcbiAgICAgICAgc291cmNlLmNvbm5lY3QoYXVkaW9Db250ZXh0UmVmLmN1cnJlbnQuZGVzdGluYXRpb24pO1xuXG4gICAgICAgIHJldHVybiBuZXcgUHJvbWlzZTx2b2lkPigocmVzb2x2ZSkgPT4ge1xuICAgICAgICAgIHNvdXJjZS5vbmVuZGVkID0gKCkgPT4ge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ1BDTSBhdWRpbyBwbGF5YmFjayBjb21wbGV0ZWQnKTtcbiAgICAgICAgICAgIHJlc29sdmUoKTtcbiAgICAgICAgICB9O1xuICAgICAgICAgIHNvdXJjZS5zdGFydCgpO1xuICAgICAgICB9KTtcblxuICAgICAgfSBjYXRjaCAocGNtRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ1BDTSBwbGF5YmFjayBmYWlsZWQsIHRyeWluZyBkaWZmZXJlbnQgc2FtcGxlIHJhdGVzOicsIHBjbUVycm9yKTtcblxuICAgICAgICAvLyBUcnkgZGlmZmVyZW50IHNhbXBsZSByYXRlc1xuICAgICAgICBjb25zdCBzYW1wbGVSYXRlcyA9IFsyNDAwMCwgMTYwMDAsIDQ0MTAwLCA0ODAwMF07XG5cbiAgICAgICAgZm9yIChjb25zdCBzYW1wbGVSYXRlIG9mIHNhbXBsZVJhdGVzKSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IGNoYW5uZWxzID0gMTtcbiAgICAgICAgICAgIGNvbnN0IGJ5dGVzUGVyU2FtcGxlID0gMjtcbiAgICAgICAgICAgIGNvbnN0IG51bVNhbXBsZXMgPSBhcnJheUJ1ZmZlci5ieXRlTGVuZ3RoIC8gYnl0ZXNQZXJTYW1wbGU7XG5cbiAgICAgICAgICAgIGNvbnN0IGF1ZGlvQnVmZmVyID0gYXVkaW9Db250ZXh0UmVmLmN1cnJlbnQuY3JlYXRlQnVmZmVyKGNoYW5uZWxzLCBudW1TYW1wbGVzLCBzYW1wbGVSYXRlKTtcbiAgICAgICAgICAgIGNvbnN0IGNoYW5uZWxEYXRhID0gYXVkaW9CdWZmZXIuZ2V0Q2hhbm5lbERhdGEoMCk7XG5cbiAgICAgICAgICAgIGNvbnN0IGRhdGFWaWV3ID0gbmV3IERhdGFWaWV3KGFycmF5QnVmZmVyKTtcbiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbnVtU2FtcGxlczsgaSsrKSB7XG4gICAgICAgICAgICAgIGNvbnN0IHNhbXBsZSA9IGRhdGFWaWV3LmdldEludDE2KGkgKiAyLCB0cnVlKTtcbiAgICAgICAgICAgICAgY2hhbm5lbERhdGFbaV0gPSBzYW1wbGUgLyAzMjc2OC4wO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBjb25zdCBzb3VyY2UgPSBhdWRpb0NvbnRleHRSZWYuY3VycmVudC5jcmVhdGVCdWZmZXJTb3VyY2UoKTtcbiAgICAgICAgICAgIHNvdXJjZS5idWZmZXIgPSBhdWRpb0J1ZmZlcjtcbiAgICAgICAgICAgIHNvdXJjZS5jb25uZWN0KGF1ZGlvQ29udGV4dFJlZi5jdXJyZW50LmRlc3RpbmF0aW9uKTtcblxuICAgICAgICAgICAgY29uc29sZS5sb2coYFBsYXlpbmcgd2l0aCBzYW1wbGUgcmF0ZTogJHtzYW1wbGVSYXRlfUh6YCk7XG5cbiAgICAgICAgICAgIHJldHVybiBuZXcgUHJvbWlzZTx2b2lkPigocmVzb2x2ZSkgPT4ge1xuICAgICAgICAgICAgICBzb3VyY2Uub25lbmRlZCA9ICgpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhgQXVkaW8gcGxheWVkIHN1Y2Nlc3NmdWxseSBhdCAke3NhbXBsZVJhdGV9SHpgKTtcbiAgICAgICAgICAgICAgICByZXNvbHZlKCk7XG4gICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgIHNvdXJjZS5zdGFydCgpO1xuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICB9IGNhdGNoIChyYXRlRXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBTYW1wbGUgcmF0ZSAke3NhbXBsZVJhdGV9SHogZmFpbGVkLCB0cnlpbmcgbmV4dC4uLmApO1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdBbGwgc2FtcGxlIHJhdGVzIGZhaWxlZCcpO1xuICAgICAgfVxuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHBsYXlpbmcgYXVkaW86JywgZXJyb3IpO1xuICAgICAgY29uc29sZS5sb2coJ0Jhc2U2NCBzYW1wbGU6JywgYmFzZTY0QXVkaW8uc3Vic3RyaW5nKDAsIDUwKSk7XG4gICAgfVxuICB9LCBbaW5pdEF1ZGlvQ29udGV4dF0pO1xuXG4gIC8vIFByb2Nlc3MgYXVkaW8gcXVldWVcbiAgY29uc3QgcHJvY2Vzc0F1ZGlvUXVldWUgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgaWYgKGlzUGxheWluZ1JlZi5jdXJyZW50IHx8IGF1ZGlvUXVldWVSZWYuY3VycmVudC5sZW5ndGggPT09IDApIHJldHVybjtcblxuICAgIGlzUGxheWluZ1JlZi5jdXJyZW50ID0gdHJ1ZTtcbiAgICBjb25zdCBhdWRpb0RhdGEgPSBhdWRpb1F1ZXVlUmVmLmN1cnJlbnQuc2hpZnQoKTtcblxuICAgIGlmIChhdWRpb0RhdGEpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IHBsYXlBdWRpbyhhdWRpb0RhdGEpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gcHJvY2Vzc0F1ZGlvUXVldWU6JywgZXJyb3IpO1xuICAgICAgICAvLyBDb250aW51ZSBwcm9jZXNzaW5nIGV2ZW4gaWYgb25lIGF1ZGlvIGNodW5rIGZhaWxzXG4gICAgICB9XG4gICAgfVxuXG4gICAgaXNQbGF5aW5nUmVmLmN1cnJlbnQgPSBmYWxzZTtcblxuICAgIC8vIFByb2Nlc3MgbmV4dCBhdWRpbyBpbiBxdWV1ZVxuICAgIGlmIChhdWRpb1F1ZXVlUmVmLmN1cnJlbnQubGVuZ3RoID4gMCkge1xuICAgICAgc2V0VGltZW91dCgoKSA9PiBwcm9jZXNzQXVkaW9RdWV1ZSgpLCA1MCk7IC8vIFNtYWxsIGRlbGF5IGJldHdlZW4gY2h1bmtzXG4gICAgfVxuICB9LCBbcGxheUF1ZGlvXSk7XG5cbiAgLy8gUGxheSBhdWRpbyBjaHVua3MgZGlyZWN0bHkgd2l0aG91dCBjb21wbGV4IGJ1ZmZlcmluZ1xuICBjb25zdCBoYW5kbGVBdWRpb0NodW5rID0gdXNlQ2FsbGJhY2soKGJhc2U2NEF1ZGlvOiBzdHJpbmcpID0+IHtcbiAgICBjb25zb2xlLmxvZygnQWRkaW5nIGF1ZGlvIGNodW5rIHRvIHF1ZXVlLCBsZW5ndGg6JywgYmFzZTY0QXVkaW8ubGVuZ3RoKTtcblxuICAgIC8vIEFkZCB0byBxdWV1ZSBmb3Igc2VxdWVudGlhbCBwbGF5YmFja1xuICAgIGF1ZGlvUXVldWVSZWYuY3VycmVudC5wdXNoKGJhc2U2NEF1ZGlvKTtcblxuICAgIC8vIFN0YXJ0IHByb2Nlc3NpbmcgcXVldWUgaWYgbm90IGFscmVhZHkgcGxheWluZ1xuICAgIGlmICghaXNQbGF5aW5nUmVmLmN1cnJlbnQpIHtcbiAgICAgIHByb2Nlc3NBdWRpb1F1ZXVlKCk7XG4gICAgfVxuICB9LCBbcHJvY2Vzc0F1ZGlvUXVldWVdKTtcblxuICBjb25zdCBzdGFydENvbnZlcnNhdGlvbiA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcbiAgICBpZiAoaXNDb25uZWN0ZWQpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICAvLyBSZXF1ZXN0IG1pY3JvcGhvbmUgcGVybWlzc2lvblxuICAgICAgYXdhaXQgbmF2aWdhdG9yLm1lZGlhRGV2aWNlcy5nZXRVc2VyTWVkaWEoeyBhdWRpbzogdHJ1ZSB9KTtcblxuICAgICAgLy8gSW5pdGlhbGl6ZSBhdWRpbyBjb250ZXh0IGZvciBwbGF5YmFja1xuICAgICAgYXdhaXQgaW5pdEF1ZGlvQ29udGV4dCgpO1xuXG4gICAgICAvLyBHZXQgc2lnbmVkIFVSTCBmb3IgV2ViU29ja2V0IGNvbm5lY3Rpb25cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvY29udmVyc2F0aW9uL3NpZ25lZC11cmw/YWdlbnRJZD0ke2FnZW50SWR9YCk7XG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGdldCBzaWduZWQgVVJMJyk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIGNvbnN0IHsgc2lnbmVkX3VybCB9ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgY29uc3Qgd2Vic29ja2V0ID0gbmV3IFdlYlNvY2tldChzaWduZWRfdXJsKTtcblxuICAgICAgd2Vic29ja2V0Lm9ub3BlbiA9IGFzeW5jICgpID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coJ1dlYlNvY2tldCBjb25uZWN0ZWQnKTtcbiAgICAgICAgc2V0SXNDb25uZWN0ZWQodHJ1ZSk7XG4gICAgICAgIHNlbmRNZXNzYWdlKHdlYnNvY2tldCwge1xuICAgICAgICAgIHR5cGU6IFwiY29udmVyc2F0aW9uX2luaXRpYXRpb25fY2xpZW50X2RhdGFcIixcbiAgICAgICAgfSk7XG4gICAgICAgIGF3YWl0IHN0YXJ0U3RyZWFtaW5nKCk7XG4gICAgICAgIHNldElzTGlzdGVuaW5nKHRydWUpO1xuICAgICAgfTtcblxuICAgICAgd2Vic29ja2V0Lm9ubWVzc2FnZSA9IGFzeW5jIChldmVudCkgPT4ge1xuICAgICAgICBjb25zdCBkYXRhID0gSlNPTi5wYXJzZShldmVudC5kYXRhKSBhcyBFbGV2ZW5MYWJzV2ViU29ja2V0RXZlbnQ7XG5cbiAgICAgICAgLy8gSGFuZGxlIHBpbmcgZXZlbnRzIHRvIGtlZXAgY29ubmVjdGlvbiBhbGl2ZVxuICAgICAgICBpZiAoZGF0YS50eXBlID09PSBcInBpbmdcIikge1xuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgc2VuZE1lc3NhZ2Uod2Vic29ja2V0LCB7XG4gICAgICAgICAgICAgIHR5cGU6IFwicG9uZ1wiLFxuICAgICAgICAgICAgICBldmVudF9pZDogZGF0YS5waW5nX2V2ZW50LmV2ZW50X2lkLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfSwgZGF0YS5waW5nX2V2ZW50LnBpbmdfbXMgfHwgMCk7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoZGF0YS50eXBlID09PSBcInVzZXJfdHJhbnNjcmlwdFwiKSB7XG4gICAgICAgICAgY29uc3QgeyB1c2VyX3RyYW5zY3JpcHRpb25fZXZlbnQgfSA9IGRhdGE7XG4gICAgICAgICAgc2V0VXNlclRyYW5zY3JpcHQodXNlcl90cmFuc2NyaXB0aW9uX2V2ZW50LnVzZXJfdHJhbnNjcmlwdCk7XG4gICAgICAgICAgY29uc29sZS5sb2coXCJVc2VyIHRyYW5zY3JpcHQ6XCIsIHVzZXJfdHJhbnNjcmlwdGlvbl9ldmVudC51c2VyX3RyYW5zY3JpcHQpO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKGRhdGEudHlwZSA9PT0gXCJhZ2VudF9yZXNwb25zZVwiKSB7XG4gICAgICAgICAgY29uc3QgeyBhZ2VudF9yZXNwb25zZV9ldmVudCB9ID0gZGF0YTtcbiAgICAgICAgICBzZXRBZ2VudFJlc3BvbnNlKGFnZW50X3Jlc3BvbnNlX2V2ZW50LmFnZW50X3Jlc3BvbnNlKTtcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIkFnZW50IHJlc3BvbnNlOlwiLCBhZ2VudF9yZXNwb25zZV9ldmVudC5hZ2VudF9yZXNwb25zZSk7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAoZGF0YS50eXBlID09PSBcImludGVycnVwdGlvblwiKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coXCJDb252ZXJzYXRpb24gaW50ZXJydXB0ZWRcIik7XG4gICAgICAgICAgLy8gQ2xlYXIgYXVkaW8gcXVldWUgb24gaW50ZXJydXB0aW9uXG4gICAgICAgICAgYXVkaW9RdWV1ZVJlZi5jdXJyZW50ID0gW107XG4gICAgICAgICAgaXNQbGF5aW5nUmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICAgICAgfVxuXG4gICAgICAgIGlmIChkYXRhLnR5cGUgPT09IFwiYXVkaW9cIikge1xuICAgICAgICAgIGNvbnN0IHsgYXVkaW9fZXZlbnQgfSA9IGRhdGE7XG4gICAgICAgICAgY29uc29sZS5sb2coJ1JlY2VpdmVkIGF1ZGlvIGNodW5rLCBsZW5ndGg6JywgYXVkaW9fZXZlbnQuYXVkaW9fYmFzZV82NC5sZW5ndGgpO1xuXG4gICAgICAgICAgLy8gRGVidWc6IENoZWNrIGF1ZGlvIGZvcm1hdCBieSBsb29raW5nIGF0IGJhc2U2NCBoZWFkZXJcbiAgICAgICAgICBjb25zdCBiYXNlNjRTYW1wbGUgPSBhdWRpb19ldmVudC5hdWRpb19iYXNlXzY0LnN1YnN0cmluZygwLCA1MCk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ0F1ZGlvIGJhc2U2NCBzYW1wbGU6JywgYmFzZTY0U2FtcGxlKTtcblxuICAgICAgICAgIC8vIFRyeSB0byBkZXRlY3QgZm9ybWF0IGZyb20gYmFzZTY0IGhlYWRlclxuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCBiaW5hcnlTdHJpbmcgPSBhdG9iKGF1ZGlvX2V2ZW50LmF1ZGlvX2Jhc2VfNjQuc3Vic3RyaW5nKDAsIDIwKSk7XG4gICAgICAgICAgICBjb25zdCBoZWFkZXIgPSBBcnJheS5mcm9tKGJpbmFyeVN0cmluZykubWFwKGMgPT4gYy5jaGFyQ29kZUF0KDApLnRvU3RyaW5nKDE2KS5wYWRTdGFydCgyLCAnMCcpKS5qb2luKCcgJyk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygnQXVkaW8gaGVhZGVyIGJ5dGVzOicsIGhlYWRlcik7XG4gICAgICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ0NvdWxkIG5vdCBkZWNvZGUgaGVhZGVyJyk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gSGFuZGxlIGF1ZGlvIGNodW5rIGRpcmVjdGx5XG4gICAgICAgICAgaGFuZGxlQXVkaW9DaHVuayhhdWRpb19ldmVudC5hdWRpb19iYXNlXzY0KTtcbiAgICAgICAgfVxuICAgICAgfTtcblxuICAgICAgd2Vic29ja2V0UmVmLmN1cnJlbnQgPSB3ZWJzb2NrZXQ7XG5cbiAgICAgIHdlYnNvY2tldC5vbmNsb3NlID0gYXN5bmMgKCkgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZygnV2ViU29ja2V0IGRpc2Nvbm5lY3RlZCcpO1xuICAgICAgICB3ZWJzb2NrZXRSZWYuY3VycmVudCA9IG51bGw7XG4gICAgICAgIHNldElzQ29ubmVjdGVkKGZhbHNlKTtcbiAgICAgICAgc2V0SXNMaXN0ZW5pbmcoZmFsc2UpO1xuICAgICAgICBzdG9wU3RyZWFtaW5nKCk7XG4gICAgICB9O1xuXG4gICAgICB3ZWJzb2NrZXQub25lcnJvciA9IChlcnJvcikgPT4ge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdXZWJTb2NrZXQgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgfTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc3RhcnQgY29udmVyc2F0aW9uOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfSwgW2FnZW50SWQsIGlzQ29ubmVjdGVkLCBzdGFydFN0cmVhbWluZywgaW5pdEF1ZGlvQ29udGV4dCwgYnVmZmVyQXVkaW9DaHVua10pO1xuXG4gIGNvbnN0IHN0b3BDb252ZXJzYXRpb24gPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF3ZWJzb2NrZXRSZWYuY3VycmVudCkgcmV0dXJuO1xuXG4gICAgd2Vic29ja2V0UmVmLmN1cnJlbnQuY2xvc2UoKTtcbiAgICBzZXRVc2VyVHJhbnNjcmlwdCgnJyk7XG4gICAgc2V0QWdlbnRSZXNwb25zZSgnJyk7XG4gICAgYXVkaW9RdWV1ZVJlZi5jdXJyZW50ID0gW107XG4gICAgYXVkaW9CdWZmZXJSZWYuY3VycmVudCA9IFtdO1xuICAgIGlzUGxheWluZ1JlZi5jdXJyZW50ID0gZmFsc2U7XG5cbiAgICAvLyBDbGVhciBidWZmZXIgdGltZW91dFxuICAgIGlmIChidWZmZXJUaW1lb3V0UmVmLmN1cnJlbnQpIHtcbiAgICAgIGNsZWFyVGltZW91dChidWZmZXJUaW1lb3V0UmVmLmN1cnJlbnQpO1xuICAgICAgYnVmZmVyVGltZW91dFJlZi5jdXJyZW50ID0gbnVsbDtcbiAgICB9XG4gIH0sIFtdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBpZiAod2Vic29ja2V0UmVmLmN1cnJlbnQpIHtcbiAgICAgICAgd2Vic29ja2V0UmVmLmN1cnJlbnQuY2xvc2UoKTtcbiAgICAgIH1cbiAgICB9O1xuICB9LCBbXSk7XG5cbiAgcmV0dXJuIHtcbiAgICBzdGFydENvbnZlcnNhdGlvbixcbiAgICBzdG9wQ29udmVyc2F0aW9uLFxuICAgIGlzQ29ubmVjdGVkLFxuICAgIGlzTGlzdGVuaW5nLFxuICAgIHVzZXJUcmFuc2NyaXB0LFxuICAgIGFnZW50UmVzcG9uc2UsXG4gIH07XG59O1xuIl0sIm5hbWVzIjpbInVzZUNhbGxiYWNrIiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlU3RhdGUiLCJ1c2VWb2ljZVN0cmVhbSIsInNlbmRNZXNzYWdlIiwid2Vic29ja2V0IiwicmVxdWVzdCIsInJlYWR5U3RhdGUiLCJXZWJTb2NrZXQiLCJPUEVOIiwic2VuZCIsIkpTT04iLCJzdHJpbmdpZnkiLCJ1c2VBZ2VudENvbnZlcnNhdGlvbiIsImFnZW50SWQiLCJ3ZWJzb2NrZXRSZWYiLCJpc0Nvbm5lY3RlZCIsInNldElzQ29ubmVjdGVkIiwidXNlclRyYW5zY3JpcHQiLCJzZXRVc2VyVHJhbnNjcmlwdCIsImFnZW50UmVzcG9uc2UiLCJzZXRBZ2VudFJlc3BvbnNlIiwiaXNMaXN0ZW5pbmciLCJzZXRJc0xpc3RlbmluZyIsImF1ZGlvQ29udGV4dFJlZiIsImF1ZGlvUXVldWVSZWYiLCJpc1BsYXlpbmdSZWYiLCJhdWRpb0J1ZmZlclJlZiIsImJ1ZmZlclRpbWVvdXRSZWYiLCJzdGFydFN0cmVhbWluZyIsInN0b3BTdHJlYW1pbmciLCJvbkF1ZGlvQ2h1bmtlZCIsImF1ZGlvRGF0YSIsImN1cnJlbnQiLCJ1c2VyX2F1ZGlvX2NodW5rIiwiaW5pdEF1ZGlvQ29udGV4dCIsIkF1ZGlvQ29udGV4dENsYXNzIiwid2luZG93IiwiQXVkaW9Db250ZXh0Iiwid2Via2l0QXVkaW9Db250ZXh0Iiwic3RhdGUiLCJyZXN1bWUiLCJlcnJvciIsImNvbnNvbGUiLCJwbGF5QXVkaW8iLCJiYXNlNjRBdWRpbyIsImxvZyIsImxlbmd0aCIsIkVycm9yIiwiYmluYXJ5U3RyaW5nIiwiYXRvYiIsImFycmF5QnVmZmVyIiwiQXJyYXlCdWZmZXIiLCJ1aW50OEFycmF5IiwiVWludDhBcnJheSIsImkiLCJjaGFyQ29kZUF0IiwiYnl0ZUxlbmd0aCIsInNhbXBsZVJhdGUiLCJjaGFubmVscyIsImJ5dGVzUGVyU2FtcGxlIiwibnVtU2FtcGxlcyIsImF1ZGlvQnVmZmVyIiwiY3JlYXRlQnVmZmVyIiwiY2hhbm5lbERhdGEiLCJnZXRDaGFubmVsRGF0YSIsImRhdGFWaWV3IiwiRGF0YVZpZXciLCJzYW1wbGUiLCJnZXRJbnQxNiIsImR1cmF0aW9uIiwic291cmNlIiwiY3JlYXRlQnVmZmVyU291cmNlIiwiYnVmZmVyIiwiY29ubmVjdCIsImRlc3RpbmF0aW9uIiwiUHJvbWlzZSIsInJlc29sdmUiLCJvbmVuZGVkIiwic3RhcnQiLCJwY21FcnJvciIsInNhbXBsZVJhdGVzIiwicmF0ZUVycm9yIiwic3Vic3RyaW5nIiwicHJvY2Vzc0F1ZGlvUXVldWUiLCJzaGlmdCIsInNldFRpbWVvdXQiLCJoYW5kbGVBdWRpb0NodW5rIiwicHVzaCIsInN0YXJ0Q29udmVyc2F0aW9uIiwibmF2aWdhdG9yIiwibWVkaWFEZXZpY2VzIiwiZ2V0VXNlck1lZGlhIiwiYXVkaW8iLCJyZXNwb25zZSIsImZldGNoIiwib2siLCJzaWduZWRfdXJsIiwianNvbiIsIm9ub3BlbiIsInR5cGUiLCJvbm1lc3NhZ2UiLCJldmVudCIsImRhdGEiLCJwYXJzZSIsImV2ZW50X2lkIiwicGluZ19ldmVudCIsInBpbmdfbXMiLCJ1c2VyX3RyYW5zY3JpcHRpb25fZXZlbnQiLCJ1c2VyX3RyYW5zY3JpcHQiLCJhZ2VudF9yZXNwb25zZV9ldmVudCIsImFnZW50X3Jlc3BvbnNlIiwiYXVkaW9fZXZlbnQiLCJhdWRpb19iYXNlXzY0IiwiYmFzZTY0U2FtcGxlIiwiaGVhZGVyIiwiQXJyYXkiLCJmcm9tIiwibWFwIiwiYyIsInRvU3RyaW5nIiwicGFkU3RhcnQiLCJqb2luIiwiZSIsIm9uY2xvc2UiLCJvbmVycm9yIiwiYnVmZmVyQXVkaW9DaHVuayIsInN0b3BDb252ZXJzYXRpb24iLCJjbG9zZSIsImNsZWFyVGltZW91dCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAgentConversation.ts\n"));

/***/ })

});
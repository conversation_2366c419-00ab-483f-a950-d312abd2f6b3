/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { OrbAvatar } from "./OrbAvatar";
import { UrlAvatar } from "./UrlAvatar";
import { ImageAvatar } from "./ImageAvatar";
export declare const WidgetConfigResponseModelAvatar: core.serialization.Schema<serializers.WidgetConfigResponseModelAvatar.Raw, ElevenLabs.WidgetConfigResponseModelAvatar>;
export declare namespace WidgetConfigResponseModelAvatar {
    type Raw = WidgetConfigResponseModelAvatar.Orb | WidgetConfigResponseModelAvatar.Url | WidgetConfigResponseModelAvatar.Image;
    interface Orb extends OrbAvatar.Raw {
        type: "orb";
    }
    interface Url extends UrlAvatar.Raw {
        type: "url";
    }
    interface Image extends ImageAvatar.Raw {
        type: "image";
    }
}

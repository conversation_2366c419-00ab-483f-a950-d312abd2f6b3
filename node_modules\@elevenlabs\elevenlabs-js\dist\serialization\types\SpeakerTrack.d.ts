/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { DubbingMediaReference } from "./DubbingMediaReference";
export declare const SpeakerTrack: core.serialization.ObjectSchema<serializers.SpeakerTrack.Raw, ElevenLabs.SpeakerTrack>;
export declare namespace SpeakerTrack {
    interface Raw {
        id: string;
        media_ref: DubbingMediaReference.Raw;
        speaker_name: string;
        voices: Record<string, string>;
        segments: string[];
    }
}

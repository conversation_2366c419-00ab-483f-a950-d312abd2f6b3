/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { SimilarVoiceCategory } from "./SimilarVoiceCategory";
export declare const SimilarVoice: core.serialization.ObjectSchema<serializers.SimilarVoice.Raw, ElevenLabs.SimilarVoice>;
export declare namespace SimilarVoice {
    interface Raw {
        voice_id: string;
        name: string;
        category: SimilarVoiceCategory.Raw;
        description?: string | null;
        preview_url?: string | null;
    }
}

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const ReaderResourceResponseModelResourceType: core.serialization.Schema<serializers.ReaderResourceResponseModelResourceType.Raw, ElevenLabs.ReaderResourceResponseModelResourceType>;
export declare namespace ReaderResourceResponseModelResourceType {
    type Raw = "read" | "collection";
}

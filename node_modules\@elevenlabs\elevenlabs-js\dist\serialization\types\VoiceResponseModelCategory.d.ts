/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const VoiceResponseModelCategory: core.serialization.Schema<serializers.VoiceResponseModelCategory.Raw, ElevenLabs.VoiceResponseModelCategory>;
export declare namespace VoiceResponseModelCategory {
    type Raw = "generated" | "cloned" | "premade" | "professional" | "famous" | "high_quality";
}

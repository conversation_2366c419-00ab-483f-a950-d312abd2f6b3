(()=>{"use strict";var __webpack_modules__={582:(e,r)=>{Object.defineProperty(r,"__esModule",{value:true});r.PARENT_MESSAGE_SETUP_ERROR=r.PARENT_MESSAGE_OK=r.PARENT_MESSAGE_CUSTOM=r.PARENT_MESSAGE_CLIENT_ERROR=r.CHILD_MESSAGE_INITIALIZE=r.CHILD_MESSAGE_END=r.CHILD_MESSAGE_CALL=void 0;const _=0;r.CHILD_MESSAGE_INITIALIZE=_;const t=1;r.CHILD_MESSAGE_CALL=t;const n=2;r.CHILD_MESSAGE_END=n;const o=0;r.PARENT_MESSAGE_OK=o;const s=1;r.PARENT_MESSAGE_CLIENT_ERROR=s;const c=2;r.PARENT_MESSAGE_SETUP_ERROR=c;const i=3;r.PARENT_MESSAGE_CUSTOM=i}};var __webpack_module_cache__={};function __nccwpck_require__(e){var r=__webpack_module_cache__[e];if(r!==undefined){return r.exports}var _=__webpack_module_cache__[e]={exports:{}};var t=true;try{__webpack_modules__[e](_,_.exports,__nccwpck_require__);t=false}finally{if(t)delete __webpack_module_cache__[e]}return _.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var __webpack_exports__={};(()=>{var _types=__nccwpck_require__(582);let file=null;let setupArgs=[];let initialized=false;const messageListener=e=>{switch(e[0]){case _types.CHILD_MESSAGE_INITIALIZE:const r=e;file=r[2];setupArgs=e[3];break;case _types.CHILD_MESSAGE_CALL:const _=e;execMethod(_[2],_[3]);break;case _types.CHILD_MESSAGE_END:end();break;default:throw new TypeError("Unexpected request from parent process: "+e[0])}};process.on("message",messageListener);function reportSuccess(e){if(!process||!process.send){throw new Error("Child can only be used on a forked process")}process.send([_types.PARENT_MESSAGE_OK,e])}function reportClientError(e){return reportError(e,_types.PARENT_MESSAGE_CLIENT_ERROR)}function reportInitializeError(e){return reportError(e,_types.PARENT_MESSAGE_SETUP_ERROR)}function reportError(e,r){if(!process||!process.send){throw new Error("Child can only be used on a forked process")}if(e==null){e=new Error('"null" or "undefined" thrown')}process.send([r,e.constructor&&e.constructor.name,e.message,e.stack,typeof e==="object"?{...e}:e])}function end(){const main=eval("require")(file);if(!main.teardown){exitProcess();return}execFunction(main.teardown,main,[],exitProcess,exitProcess)}function exitProcess(){process.removeListener("message",messageListener)}function execMethod(method,args){const main=eval("require")(file);let fn;if(method==="default"){fn=main.__esModule?main["default"]:main}else{fn=main[method]}function execHelper(){execFunction(fn,main,args,reportSuccess,reportClientError)}if(initialized||!main.setup){execHelper();return}initialized=true;execFunction(main.setup,main,setupArgs,execHelper,reportInitializeError)}const isPromise=e=>!!e&&(typeof e==="object"||typeof e==="function")&&typeof e.then==="function";function execFunction(e,r,_,t,n){let o;try{o=e.apply(r,_)}catch(e){n(e);return}if(isPromise(o)){o.then(t,n)}else{t(o)}}})();module.exports=__webpack_exports__})();
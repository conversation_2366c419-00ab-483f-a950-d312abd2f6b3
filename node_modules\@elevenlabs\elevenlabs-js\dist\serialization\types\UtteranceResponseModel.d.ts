/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const UtteranceResponseModel: core.serialization.ObjectSchema<serializers.UtteranceResponseModel.Raw, ElevenLabs.UtteranceResponseModel>;
export declare namespace UtteranceResponseModel {
    interface Raw {
        start: number;
        end: number;
    }
}

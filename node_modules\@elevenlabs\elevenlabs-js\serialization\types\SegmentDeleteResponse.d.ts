/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SegmentDeleteResponse: core.serialization.ObjectSchema<serializers.SegmentDeleteResponse.Raw, ElevenLabs.SegmentDeleteResponse>;
export declare namespace SegmentDeleteResponse {
    interface Raw {
        version: number;
    }
}

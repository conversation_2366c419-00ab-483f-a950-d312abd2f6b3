/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { LiteralJsonSchemaProperty } from "./LiteralJsonSchemaProperty";
export declare const QueryParamsJsonSchema: core.serialization.ObjectSchema<serializers.QueryParamsJsonSchema.Raw, ElevenLabs.QueryParamsJsonSchema>;
export declare namespace QueryParamsJsonSchema {
    interface Raw {
        properties: Record<string, LiteralJsonSchemaProperty.Raw>;
        required?: string[] | null;
    }
}

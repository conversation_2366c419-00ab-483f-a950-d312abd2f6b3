'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAgentConversation } from '@/hooks/useAgentConversation';

export default function VoiceChatPage() {
  const params = useParams();
  const router = useRouter();
  const agentId = params.agentId as string;

  const [agentName, setAgentName] = useState('AI Agent');
  const [error, setError] = useState<string | null>(null);

  const {
    startConversation,
    stopConversation,
    isConnected,
    isListening,
    userTranscript,
    agentResponse,
  } = useAgentConversation(agentId);

  useEffect(() => {
    // Fetch agent info to get the name
    fetchAgentInfo();
  }, [agentId]);

  const fetchAgentInfo = async () => {
    try {
      const response = await fetch('/api/agents');
      if (response.ok) {
        const data = await response.json();
        const agent = data.agents.find((a: any) => a.agentId === agentId);
        if (agent) {
          setAgentName(agent.name);
        }
      }
    } catch (error) {
      console.error('Error fetching agent info:', error);
    }
  };

  const handleStartConversation = useCallback(async () => {
    try {
      setError(null);
      await startConversation();
    } catch (error) {
      console.error('Failed to start conversation:', error);
      setError('Failed to start conversation. Please check your microphone permissions.');
    }
  }, [startConversation]);

  const handleStopConversation = useCallback(async () => {
    try {
      await stopConversation();
    } catch (error) {
      console.error('Failed to stop conversation:', error);
    }
  }, [stopConversation]);



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex flex-col">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 p-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.push('/')}
              className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            >
              ← Back
            </button>
            <h1 className="text-xl font-semibold text-gray-800 dark:text-white">
              Voice Chat with {agentName}
            </h1>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col items-center justify-center p-8">
        <div className="max-w-2xl w-full text-center space-y-8">

          {/* Voice Visualization */}
          <div className="relative">
            <div className={`w-32 h-32 mx-auto rounded-full border-4 transition-all duration-300 ${
              isListening
                ? 'border-green-500 bg-green-100 dark:bg-green-900 animate-pulse'
                : 'border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-800'
            }`}>
              <div className="flex items-center justify-center h-full">
                <svg
                  className={`w-12 h-12 ${isListening ? 'text-green-600' : 'text-gray-400'}`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
            {isListening && (
              <div className="absolute inset-0 w-32 h-32 mx-auto rounded-full border-4 border-green-300 animate-ping"></div>
            )}
          </div>

          {/* Status */}
          <div className="space-y-4">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
              {isConnected
                ? (isListening ? 'Listening...' : 'Ready to chat')
                : 'Not connected'
              }
            </h2>

            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {error}
              </div>
            )}
          </div>

          {/* Controls */}
          <div className="flex justify-center space-x-4">
            {!isConnected ? (
              <button
                onClick={handleStartConversation}
                className="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-8 rounded-full text-lg transition-colors duration-200 flex items-center space-x-2"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
                </svg>
                <span>Start Voice Chat</span>
              </button>
            ) : (
              <button
                onClick={handleStopConversation}
                className="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-8 rounded-full text-lg transition-colors duration-200 flex items-center space-x-2"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd" />
                </svg>
                <span>Stop Chat</span>
              </button>
            )}
          </div>

          {/* Transcripts */}
          {(userTranscript || agentResponse) && (
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg space-y-4">
              {userTranscript && (
                <div className="text-left">
                  <h3 className="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">You said:</h3>
                  <p className="text-gray-800 dark:text-white bg-blue-50 dark:bg-blue-900 p-3 rounded">
                    {userTranscript}
                  </p>
                </div>
              )}

              {agentResponse && (
                <div className="text-left">
                  <h3 className="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">{agentName} responded:</h3>
                  <p className="text-gray-800 dark:text-white bg-green-50 dark:bg-green-900 p-3 rounded">
                    {agentResponse}
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Instructions */}
          <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
            <p>🎤 Click "Start Voice Chat" to begin talking with {agentName}</p>
            <p>🔊 Make sure your microphone and speakers are working</p>
            <p>💬 Speak naturally - the AI will respond with voice</p>
          </div>
        </div>
      </div>
    </div>
  );
}

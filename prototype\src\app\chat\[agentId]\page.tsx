'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAgentConversation } from '@/hooks/useAgentConversation';

export default function VoiceChatPage() {
  const params = useParams();
  const router = useRouter();
  const agentId = params.agentId as string;

  const [agentName, setAgentName] = useState('AI Agent');
  const [error, setError] = useState<string | null>(null);

  const {
    startConversation,
    stopConversation,
    isConnected,
    isListening,
    userTranscript,
    agentResponse,
  } = useAgentConversation(agentId);

  useEffect(() => {
    // Fetch agent info to get the name
    fetchAgentInfo();
  }, [agentId]);

  const fetchAgentInfo = async () => {
    try {
      const response = await fetch('/api/agents');
      if (response.ok) {
        const data = await response.json();
        const agent = data.agents.find((a: any) => a.agentId === agentId);
        if (agent) {
          setAgentName(agent.name);
        }
      }
    } catch (error) {
      console.error('Error fetching agent info:', error);
    }
  };

  const handleStartConversation = useCallback(async () => {
    try {
      setError(null);
      await startConversation();
    } catch (error) {
      console.error('Failed to start conversation:', error);
      setError('Failed to start conversation. Please check your microphone permissions.');
    }
  }, [startConversation]);

  const handleStopConversation = useCallback(async () => {
    try {
      await stopConversation();
    } catch (error) {
      console.error('Failed to stop conversation:', error);
    }
  }, [stopConversation]);



  const getAgentImage = (agentName: string) => {
    const imageMap: { [key: string]: string } = {
      'Cantika': '/Cantikapp.png',
    };
    return imageMap[agentName] || '/default-avatar.png';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-slate-800 dark:to-gray-800 flex flex-col">
      {/* Header */}
      <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-lg border-b border-white/20 dark:border-gray-700/50 p-6">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <button
              onClick={() => router.push('/')}
              className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 group"
            >
              <svg className="w-5 h-5 transform group-hover:-translate-x-1 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
              <span className="font-medium">Back to Agents</span>
            </button>

            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 p-0.5 shadow-lg">
                <img
                  src={getAgentImage(agentName)}
                  alt={agentName}
                  className="w-full h-full rounded-full object-cover bg-white"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const parent = target.parentElement;
                    if (parent) {
                      parent.innerHTML = `
                        <div class="w-full h-full rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-lg font-bold">
                          ${agentName.charAt(0)}
                        </div>
                      `;
                    }
                  }}
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
                  {agentName}
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  AI Voice Assistant
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <div className={`flex items-center space-x-2 px-3 py-2 rounded-full ${
              isConnected
                ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300'
                : 'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300'
            }`}>
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'} ${isConnected ? 'animate-pulse' : ''}`}></div>
              <span className="text-sm font-medium">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col items-center justify-center p-8 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-blue-400/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-32 h-32 bg-purple-400/10 rounded-full blur-xl animate-pulse delay-1000"></div>

        <div className="max-w-4xl w-full text-center space-y-12 relative z-10">

          {/* Voice Visualization */}
          <div className="relative">
            <div className={`w-40 h-40 mx-auto rounded-full transition-all duration-500 relative ${
              isListening
                ? 'bg-gradient-to-r from-green-400 to-blue-500 shadow-2xl shadow-green-500/50'
                : 'bg-gradient-to-r from-gray-300 to-gray-400 dark:from-gray-600 dark:to-gray-700'
            }`}>
              {/* Outer Ring */}
              <div className={`absolute inset-2 rounded-full border-4 transition-all duration-300 ${
                isListening
                  ? 'border-white/30 animate-pulse'
                  : 'border-white/20'
              }`}></div>

              {/* Inner Content */}
              <div className="flex items-center justify-center h-full">
                <svg
                  className={`w-16 h-16 transition-all duration-300 ${
                    isListening ? 'text-white scale-110' : 'text-white/70'
                  }`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
                </svg>
              </div>
            </div>

            {/* Animated Rings */}
            {isListening && (
              <>
                <div className="absolute inset-0 w-40 h-40 mx-auto rounded-full border-4 border-green-400/30 animate-ping"></div>
                <div className="absolute inset-0 w-40 h-40 mx-auto rounded-full border-4 border-blue-400/20 animate-ping delay-300"></div>
                <div className="absolute inset-0 w-40 h-40 mx-auto rounded-full border-4 border-purple-400/10 animate-ping delay-700"></div>
              </>
            )}
          </div>

          {/* Status */}
          <div className="space-y-6">
            <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-lg rounded-3xl p-8 border border-white/20 dark:border-gray-700/50 shadow-xl">
              <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-4">
                {isConnected
                  ? (isListening ? '🎤 Listening...' : '✨ Ready to chat')
                  : '🔌 Not connected'
                }
              </h2>

              <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
                {isConnected
                  ? (isListening
                      ? `Speak naturally to ${agentName}. I'm listening and will respond with voice.`
                      : `Click the microphone to start talking with ${agentName}.`
                    )
                  : `Connect to start your voice conversation with ${agentName}.`
                }
              </p>

              {error && (
                <div className="bg-red-100 dark:bg-red-900/50 border border-red-300 dark:border-red-700 text-red-700 dark:text-red-300 px-6 py-4 rounded-2xl mb-6">
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <span className="font-medium">{error}</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Controls */}
          <div className="flex justify-center space-x-6">
            {!isConnected ? (
              <button
                onClick={handleStartConversation}
                className="group bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-bold py-4 px-10 rounded-2xl text-xl transition-all duration-300 flex items-center space-x-3 shadow-2xl shadow-green-500/30 transform hover:scale-105"
              >
                <svg className="w-6 h-6 group-hover:scale-110 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
                </svg>
                <span>Start Voice Chat</span>
              </button>
            ) : (
              <button
                onClick={handleStopConversation}
                className="group bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white font-bold py-4 px-10 rounded-2xl text-xl transition-all duration-300 flex items-center space-x-3 shadow-2xl shadow-red-500/30 transform hover:scale-105"
              >
                <svg className="w-6 h-6 group-hover:scale-110 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd" />
                </svg>
                <span>End Chat</span>
              </button>
            )}
          </div>

          {/* Transcripts */}
          {(userTranscript || agentResponse) && (
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-lg rounded-3xl p-8 shadow-2xl border border-white/20 dark:border-gray-700/50 space-y-6 max-w-4xl mx-auto">
              {userTranscript && (
                <div className="text-left">
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300">You said:</h3>
                  </div>
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 p-4 rounded-2xl border border-blue-200/50 dark:border-blue-700/50">
                    <p className="text-gray-800 dark:text-white text-lg leading-relaxed">
                      "{userTranscript}"
                    </p>
                  </div>
                </div>
              )}

              {agentResponse && (
                <div className="text-left">
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-green-400 to-emerald-500 flex items-center justify-center">
                      <img
                        src={getAgentImage(agentName)}
                        alt={agentName}
                        className="w-full h-full rounded-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const parent = target.parentElement;
                          if (parent) {
                            parent.innerHTML = `
                              <div class="w-full h-full rounded-full bg-gradient-to-r from-green-500 to-emerald-600 flex items-center justify-center text-white text-sm font-bold">
                                ${agentName.charAt(0)}
                              </div>
                            `;
                          }
                        }}
                      />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300">{agentName} responded:</h3>
                  </div>
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30 p-4 rounded-2xl border border-green-200/50 dark:border-green-700/50">
                    <p className="text-gray-800 dark:text-white text-lg leading-relaxed">
                      "{agentResponse}"
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Instructions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="text-center p-6 bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-gray-700/50">
              <div className="text-3xl mb-3">🎤</div>
              <h4 className="font-semibold text-gray-800 dark:text-white mb-2">Start Talking</h4>
              <p className="text-sm text-gray-600 dark:text-gray-300">Click the button and speak naturally to {agentName}</p>
            </div>

            <div className="text-center p-6 bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-gray-700/50">
              <div className="text-3xl mb-3">🔊</div>
              <h4 className="font-semibold text-gray-800 dark:text-white mb-2">Listen</h4>
              <p className="text-sm text-gray-600 dark:text-gray-300">Hear {agentName}'s voice responses in real-time</p>
            </div>

            <div className="text-center p-6 bg-white/40 dark:bg-gray-800/40 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-gray-700/50">
              <div className="text-3xl mb-3">💬</div>
              <h4 className="font-semibold text-gray-800 dark:text-white mb-2">Natural Chat</h4>
              <p className="text-sm text-gray-600 dark:text-gray-300">Have a natural conversation like talking to a friend</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SegmentTranslationResponse: core.serialization.ObjectSchema<serializers.SegmentTranslationResponse.Raw, ElevenLabs.SegmentTranslationResponse>;
export declare namespace SegmentTranslationResponse {
    interface Raw {
        version: number;
    }
}

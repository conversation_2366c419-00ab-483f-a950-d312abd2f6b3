/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { WebhookAuthMethodType } from "./WebhookAuthMethodType";
import { WorkspaceWebhookUsageResponseModel } from "./WorkspaceWebhookUsageResponseModel";
export declare const WorkspaceWebhookResponseModel: core.serialization.ObjectSchema<serializers.WorkspaceWebhookResponseModel.Raw, ElevenLabs.WorkspaceWebhookResponseModel>;
export declare namespace WorkspaceWebhookResponseModel {
    interface Raw {
        name: string;
        webhook_id: string;
        webhook_url: string;
        is_disabled: boolean;
        is_auto_disabled: boolean;
        created_at_unix: number;
        auth_type: WebhookAuthMethodType.Raw;
        usage?: WorkspaceWebhookUsageResponseModel.Raw[] | null;
        most_recent_failure_error_code?: number | null;
        most_recent_failure_timestamp?: number | null;
    }
}

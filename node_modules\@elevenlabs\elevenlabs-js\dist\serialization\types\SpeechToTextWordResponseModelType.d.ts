/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const SpeechToTextWordResponseModelType: core.serialization.Schema<serializers.SpeechToTextWordResponseModelType.Raw, ElevenLabs.SpeechToTextWordResponseModelType>;
export declare namespace SpeechToTextWordResponseModelType {
    type Raw = "word" | "spacing" | "audio_event";
}

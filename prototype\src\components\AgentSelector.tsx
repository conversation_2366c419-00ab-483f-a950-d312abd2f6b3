'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface Agent {
  agentId: string;
  name: string;
  tags: string[];
  createdAtUnixSecs: number;
  accessInfo: {
    isCreator: boolean;
    creatorName: string;
    creatorEmail: string;
    role: string;
  };
}

interface AgentsResponse {
  agents: Agent[];
  hasMore: boolean;
  nextCursor?: string;
}

export default function AgentSelector() {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    fetchAgents();
  }, []);

  const fetchAgents = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/agents');
      
      if (!response.ok) {
        throw new Error('Failed to fetch agents');
      }
      
      const data: AgentsResponse = await response.json();
      setAgents(data.agents);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleAgentSelect = (agentId: string) => {
    router.push(`/chat/${agentId}`);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          Error: {error}
        </div>
        <button
          onClick={fetchAgents}
          className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (agents.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600 dark:text-gray-400">No agents available</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
      {agents.map((agent) => (
        <div
          key={agent.agentId}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6 cursor-pointer border border-gray-200 dark:border-gray-700"
          onClick={() => handleAgentSelect(agent.agentId)}
        >
          <div className="mb-4">
            <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
              {agent.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Created by {agent.accessInfo.creatorName}
            </p>
          </div>

          {agent.tags.length > 0 && (
            <div className="mb-4">
              <div className="flex flex-wrap gap-2">
                {agent.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium px-2.5 py-0.5 rounded"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {new Date(agent.createdAtUnixSecs * 1000).toLocaleDateString()}
            </span>
            <button className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm transition-colors duration-200">
              Chat Now
            </button>
          </div>
        </div>
      ))}
    </div>
  );
}

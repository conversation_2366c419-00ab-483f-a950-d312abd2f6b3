/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ShareOptionResponseModelType } from "./ShareOptionResponseModelType";
export declare const ShareOptionResponseModel: core.serialization.ObjectSchema<serializers.ShareOptionResponseModel.Raw, ElevenLabs.ShareOptionResponseModel>;
export declare namespace ShareOptionResponseModel {
    interface Raw {
        name: string;
        id: string;
        type: ShareOptionResponseModelType.Raw;
    }
}

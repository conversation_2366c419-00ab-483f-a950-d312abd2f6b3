/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { WidgetTextContents } from "./WidgetTextContents";
export declare const WidgetLanguagePresetResponse: core.serialization.ObjectSchema<serializers.WidgetLanguagePresetResponse.Raw, ElevenLabs.WidgetLanguagePresetResponse>;
export declare namespace WidgetLanguagePresetResponse {
    interface Raw {
        first_message?: string | null;
        text_contents?: WidgetTextContents.Raw | null;
    }
}

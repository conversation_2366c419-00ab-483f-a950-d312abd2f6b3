/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { SpeechToTextWordResponseModel } from "./SpeechToTextWordResponseModel";
import { AdditionalFormatResponseModel } from "./AdditionalFormatResponseModel";
export declare const SpeechToTextChunkResponseModel: core.serialization.ObjectSchema<serializers.SpeechToTextChunkResponseModel.Raw, ElevenLabs.SpeechToTextChunkResponseModel>;
export declare namespace SpeechToTextChunkResponseModel {
    interface Raw {
        language_code: string;
        language_probability: number;
        text: string;
        words: SpeechToTextWordResponseModel.Raw[];
        additional_formats?: (AdditionalFormatResponseModel.Raw | null | undefined)[] | null;
    }
}

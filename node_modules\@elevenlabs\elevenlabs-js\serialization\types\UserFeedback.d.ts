/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { UserFeedbackScore } from "./UserFeedbackScore";
export declare const UserFeedback: core.serialization.ObjectSchema<serializers.UserFeedback.Raw, ElevenLabs.UserFeedback>;
export declare namespace UserFeedback {
    interface Raw {
        score: UserFeedbackScore.Raw;
        time_in_call_secs: number;
    }
}

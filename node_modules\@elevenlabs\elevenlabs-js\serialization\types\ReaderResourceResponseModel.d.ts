/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { ReaderResourceResponseModelResourceType } from "./ReaderResourceResponseModelResourceType";
export declare const ReaderResourceResponseModel: core.serialization.ObjectSchema<serializers.ReaderResourceResponseModel.Raw, ElevenLabs.ReaderResourceResponseModel>;
export declare namespace ReaderResourceResponseModel {
    interface Raw {
        resource_type: ReaderResourceResponseModelResourceType.Raw;
        resource_id: string;
    }
}

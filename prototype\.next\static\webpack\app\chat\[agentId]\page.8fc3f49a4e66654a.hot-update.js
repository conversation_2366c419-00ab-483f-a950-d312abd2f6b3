"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[agentId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useAgentConversation.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useAgentConversation.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAgentConversation: () => (/* binding */ useAgentConversation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var voice_stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! voice-stream */ \"(app-pages-browser)/../node_modules/voice-stream/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAgentConversation auto */ \n\nconst sendMessage = (websocket, request)=>{\n    if (websocket.readyState !== WebSocket.OPEN) {\n        return;\n    }\n    websocket.send(JSON.stringify(request));\n};\nconst useAgentConversation = (agentId)=>{\n    const websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [userTranscript, setUserTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [agentResponse, setAgentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isPlayingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const audioBufferRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const bufferTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { startStreaming, stopStreaming } = (0,voice_stream__WEBPACK_IMPORTED_MODULE_1__.useVoiceStream)({\n        onAudioChunked: {\n            \"useAgentConversation.useVoiceStream\": (audioData)=>{\n                if (!websocketRef.current) return;\n                sendMessage(websocketRef.current, {\n                    user_audio_chunk: audioData\n                });\n            }\n        }[\"useAgentConversation.useVoiceStream\"]\n    });\n    // Initialize audio context\n    const initAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[initAudioContext]\": async ()=>{\n            if (!audioContextRef.current) {\n                try {\n                    const AudioContextClass = window.AudioContext || window.webkitAudioContext;\n                    audioContextRef.current = new AudioContextClass();\n                    // Resume audio context if it's suspended (required by some browsers)\n                    if (audioContextRef.current.state === 'suspended') {\n                        await audioContextRef.current.resume();\n                    }\n                } catch (error) {\n                    console.error('Failed to initialize audio context:', error);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[initAudioContext]\"], []);\n    // Play audio from base64 using Web Audio API\n    const playAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[playAudio]\": async (base64Audio)=>{\n            try {\n                console.log('Attempting to play audio chunk, length:', base64Audio.length);\n                // Initialize audio context if needed\n                if (!audioContextRef.current) {\n                    await initAudioContext();\n                }\n                if (!audioContextRef.current) {\n                    throw new Error('Audio context not available');\n                }\n                // Method 1: Try Web Audio API with proper decoding\n                try {\n                    // Convert base64 to ArrayBuffer\n                    const binaryString = atob(base64Audio);\n                    const arrayBuffer = new ArrayBuffer(binaryString.length);\n                    const uint8Array = new Uint8Array(arrayBuffer);\n                    for(let i = 0; i < binaryString.length; i++){\n                        uint8Array[i] = binaryString.charCodeAt(i);\n                    }\n                    console.log('Decoded audio buffer size:', arrayBuffer.byteLength);\n                    // Decode audio data\n                    const audioBuffer = await audioContextRef.current.decodeAudioData(arrayBuffer);\n                    console.log('Audio buffer decoded successfully:', audioBuffer.duration, 'seconds');\n                    // Create and play audio source\n                    const source = audioContextRef.current.createBufferSource();\n                    source.buffer = audioBuffer;\n                    source.connect(audioContextRef.current.destination);\n                    return new Promise({\n                        \"useAgentConversation.useCallback[playAudio]\": (resolve)=>{\n                            source.onended = ({\n                                \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                    console.log('Audio playback completed');\n                                    resolve();\n                                }\n                            })[\"useAgentConversation.useCallback[playAudio]\"];\n                            source.start();\n                        }\n                    }[\"useAgentConversation.useCallback[playAudio]\"]);\n                } catch (webAudioError) {\n                    console.log('Web Audio API failed, trying Blob approach:', webAudioError);\n                    // Method 2: Try Blob with different MIME types\n                    const mimeTypes = [\n                        'audio/mpeg',\n                        'audio/mp3',\n                        'audio/wav',\n                        'audio/ogg',\n                        'audio/webm',\n                        'audio/mp4',\n                        'audio/aac'\n                    ];\n                    for (const mimeType of mimeTypes){\n                        try {\n                            // Convert base64 to Blob\n                            const binaryString = atob(base64Audio);\n                            const bytes = new Uint8Array(binaryString.length);\n                            for(let i = 0; i < binaryString.length; i++){\n                                bytes[i] = binaryString.charCodeAt(i);\n                            }\n                            const blob = new Blob([\n                                bytes\n                            ], {\n                                type: mimeType\n                            });\n                            const audioUrl = URL.createObjectURL(blob);\n                            const audio = new Audio(audioUrl);\n                            audio.volume = 0.8;\n                            const playPromise = new Promise({\n                                \"useAgentConversation.useCallback[playAudio]\": (resolve, reject)=>{\n                                    let resolved = false;\n                                    const cleanup = {\n                                        \"useAgentConversation.useCallback[playAudio].cleanup\": ()=>{\n                                            URL.revokeObjectURL(audioUrl);\n                                            audio.onended = null;\n                                            audio.onerror = null;\n                                            audio.oncanplaythrough = null;\n                                        }\n                                    }[\"useAgentConversation.useCallback[playAudio].cleanup\"];\n                                    audio.onended = ({\n                                        \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                            if (!resolved) {\n                                                resolved = true;\n                                                cleanup();\n                                                console.log(\"Audio played successfully with MIME type: \".concat(mimeType));\n                                                resolve();\n                                            }\n                                        }\n                                    })[\"useAgentConversation.useCallback[playAudio]\"];\n                                    audio.onerror = ({\n                                        \"useAgentConversation.useCallback[playAudio]\": (error)=>{\n                                            if (!resolved) {\n                                                cleanup();\n                                                reject(error);\n                                            }\n                                        }\n                                    })[\"useAgentConversation.useCallback[playAudio]\"];\n                                    audio.oncanplaythrough = ({\n                                        \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                            console.log(\"Audio ready with MIME type: \".concat(mimeType));\n                                            audio.play().catch(reject);\n                                        }\n                                    })[\"useAgentConversation.useCallback[playAudio]\"];\n                                    // Fallback\n                                    setTimeout({\n                                        \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                            if (!resolved && audio.readyState >= 2) {\n                                                audio.play().catch(reject);\n                                            }\n                                        }\n                                    }[\"useAgentConversation.useCallback[playAudio]\"], 300);\n                                }\n                            }[\"useAgentConversation.useCallback[playAudio]\"]);\n                            await playPromise;\n                            return; // Success\n                        } catch (blobError) {\n                            console.log(\"MIME type \".concat(mimeType, \" failed:\"), blobError);\n                            continue;\n                        }\n                    }\n                    throw new Error('All playback methods failed');\n                }\n            } catch (error) {\n                console.error('Error playing audio:', error);\n                // Log first few characters of base64 for debugging\n                console.log('Base64 sample:', base64Audio.substring(0, 100));\n            }\n        }\n    }[\"useAgentConversation.useCallback[playAudio]\"], [\n        initAudioContext\n    ]);\n    // Process audio queue\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingRef.current || audioQueueRef.current.length === 0) return;\n            isPlayingRef.current = true;\n            const audioData = audioQueueRef.current.shift();\n            if (audioData) {\n                try {\n                    await playAudio(audioData);\n                } catch (error) {\n                    console.error('Error in processAudioQueue:', error);\n                // Continue processing even if one audio chunk fails\n                }\n            }\n            isPlayingRef.current = false;\n            // Process next audio in queue\n            if (audioQueueRef.current.length > 0) {\n                setTimeout({\n                    \"useAgentConversation.useCallback[processAudioQueue]\": ()=>processAudioQueue()\n                }[\"useAgentConversation.useCallback[processAudioQueue]\"], 50); // Small delay between chunks\n            }\n        }\n    }[\"useAgentConversation.useCallback[processAudioQueue]\"], [\n        playAudio\n    ]);\n    // Buffer audio chunks and play when ready\n    const bufferAudioChunk = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[bufferAudioChunk]\": (base64Audio)=>{\n            try {\n                // Decode base64 to binary data\n                const binaryString = atob(base64Audio);\n                const bytes = new Uint8Array(binaryString.length);\n                for(let i = 0; i < binaryString.length; i++){\n                    bytes[i] = binaryString.charCodeAt(i);\n                }\n                // Add decoded bytes to buffer\n                audioBufferRef.current.push(bytes);\n                console.log('Added audio chunk to buffer, chunks count:', audioBufferRef.current.length);\n                // Clear existing timeout\n                if (bufferTimeoutRef.current) {\n                    clearTimeout(bufferTimeoutRef.current);\n                }\n                // Set timeout to play buffered audio\n                bufferTimeoutRef.current = setTimeout({\n                    \"useAgentConversation.useCallback[bufferAudioChunk]\": ()=>{\n                        if (audioBufferRef.current.length > 0) {\n                            // Concatenate all buffered chunks\n                            const totalLength = audioBufferRef.current.reduce({\n                                \"useAgentConversation.useCallback[bufferAudioChunk].totalLength\": (sum, chunk)=>sum + chunk.length\n                            }[\"useAgentConversation.useCallback[bufferAudioChunk].totalLength\"], 0);\n                            const combinedBuffer = new Uint8Array(totalLength);\n                            let offset = 0;\n                            for (const chunk of audioBufferRef.current){\n                                combinedBuffer.set(chunk, offset);\n                                offset += chunk.length;\n                            }\n                            console.log('Playing buffered audio, total bytes:', totalLength);\n                            // Convert back to base64 for playback\n                            const combinedBase64 = btoa(String.fromCharCode(...combinedBuffer));\n                            audioQueueRef.current.push(combinedBase64);\n                            audioBufferRef.current = []; // Clear buffer\n                            processAudioQueue();\n                        }\n                    }\n                }[\"useAgentConversation.useCallback[bufferAudioChunk]\"], 300); // Wait 300ms for more chunks\n            } catch (error) {\n                console.error('Error buffering audio chunk:', error);\n            }\n        }\n    }[\"useAgentConversation.useCallback[bufferAudioChunk]\"], [\n        processAudioQueue\n    ]);\n    const startConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n            if (isConnected) return;\n            try {\n                // Request microphone permission\n                await navigator.mediaDevices.getUserMedia({\n                    audio: true\n                });\n                // Initialize audio context for playback\n                await initAudioContext();\n                // Get signed URL for WebSocket connection\n                const response = await fetch(\"/api/conversation/signed-url?agentId=\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to get signed URL');\n                }\n                const { signed_url } = await response.json();\n                const websocket = new WebSocket(signed_url);\n                websocket.onopen = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        sendMessage(websocket, {\n                            type: \"conversation_initiation_client_data\"\n                        });\n                        await startStreaming();\n                        setIsListening(true);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onmessage = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async (event)=>{\n                        const data = JSON.parse(event.data);\n                        // Handle ping events to keep connection alive\n                        if (data.type === \"ping\") {\n                            setTimeout({\n                                \"useAgentConversation.useCallback[startConversation]\": ()=>{\n                                    sendMessage(websocket, {\n                                        type: \"pong\",\n                                        event_id: data.ping_event.event_id\n                                    });\n                                }\n                            }[\"useAgentConversation.useCallback[startConversation]\"], data.ping_event.ping_ms || 0);\n                        }\n                        if (data.type === \"user_transcript\") {\n                            const { user_transcription_event } = data;\n                            setUserTranscript(user_transcription_event.user_transcript);\n                            console.log(\"User transcript:\", user_transcription_event.user_transcript);\n                        }\n                        if (data.type === \"agent_response\") {\n                            const { agent_response_event } = data;\n                            setAgentResponse(agent_response_event.agent_response);\n                            console.log(\"Agent response:\", agent_response_event.agent_response);\n                        }\n                        if (data.type === \"interruption\") {\n                            console.log(\"Conversation interrupted\");\n                            // Clear audio queue on interruption\n                            audioQueueRef.current = [];\n                            isPlayingRef.current = false;\n                        }\n                        if (data.type === \"audio\") {\n                            const { audio_event } = data;\n                            console.log('Received audio chunk, length:', audio_event.audio_base_64.length);\n                            // Debug: Check audio format by looking at base64 header\n                            const base64Sample = audio_event.audio_base_64.substring(0, 50);\n                            console.log('Audio base64 sample:', base64Sample);\n                            // Try to detect format from base64 header\n                            try {\n                                const binaryString = atob(audio_event.audio_base_64.substring(0, 20));\n                                const header = Array.from(binaryString).map({\n                                    \"useAgentConversation.useCallback[startConversation].header\": (c)=>c.charCodeAt(0).toString(16).padStart(2, '0')\n                                }[\"useAgentConversation.useCallback[startConversation].header\"]).join(' ');\n                                console.log('Audio header bytes:', header);\n                            } catch (e) {\n                                console.log('Could not decode header');\n                            }\n                            // Buffer audio chunks instead of playing immediately\n                            bufferAudioChunk(audio_event.audio_base_64);\n                        }\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocketRef.current = websocket;\n                websocket.onclose = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket disconnected');\n                        websocketRef.current = null;\n                        setIsConnected(false);\n                        setIsListening(false);\n                        stopStreaming();\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onerror = ({\n                    \"useAgentConversation.useCallback[startConversation]\": (error)=>{\n                        console.error('WebSocket error:', error);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                throw error;\n            }\n        }\n    }[\"useAgentConversation.useCallback[startConversation]\"], [\n        agentId,\n        isConnected,\n        startStreaming,\n        initAudioContext,\n        bufferAudioChunk\n    ]);\n    const stopConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[stopConversation]\": async ()=>{\n            if (!websocketRef.current) return;\n            websocketRef.current.close();\n            setUserTranscript('');\n            setAgentResponse('');\n            audioQueueRef.current = [];\n            audioBufferRef.current = [];\n            isPlayingRef.current = false;\n            // Clear buffer timeout\n            if (bufferTimeoutRef.current) {\n                clearTimeout(bufferTimeoutRef.current);\n                bufferTimeoutRef.current = null;\n            }\n        }\n    }[\"useAgentConversation.useCallback[stopConversation]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAgentConversation.useEffect\": ()=>{\n            return ({\n                \"useAgentConversation.useEffect\": ()=>{\n                    if (websocketRef.current) {\n                        websocketRef.current.close();\n                    }\n                }\n            })[\"useAgentConversation.useEffect\"];\n        }\n    }[\"useAgentConversation.useEffect\"], []);\n    return {\n        startConversation,\n        stopConversation,\n        isConnected,\n        isListening,\n        userTranscript,\n        agentResponse\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAgentConversation.ts\n"));

/***/ })

});
/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { SimilarVoice } from "./SimilarVoice";
export declare const SimilarVoicesForSpeakerResponse: core.serialization.ObjectSchema<serializers.SimilarVoicesForSpeakerResponse.Raw, ElevenLabs.SimilarVoicesForSpeakerResponse>;
export declare namespace SimilarVoicesForSpeakerResponse {
    interface Raw {
        voices: SimilarVoice.Raw[];
    }
}

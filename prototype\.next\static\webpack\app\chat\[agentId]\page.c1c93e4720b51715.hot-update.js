"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/[agentId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useAgentConversation.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useAgentConversation.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAgentConversation: () => (/* binding */ useAgentConversation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var voice_stream__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! voice-stream */ \"(app-pages-browser)/../node_modules/voice-stream/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ useAgentConversation auto */ \n\nconst sendMessage = (websocket, request)=>{\n    if (websocket.readyState !== WebSocket.OPEN) {\n        return;\n    }\n    websocket.send(JSON.stringify(request));\n};\nconst useAgentConversation = (agentId)=>{\n    const websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [userTranscript, setUserTranscript] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [agentResponse, setAgentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const audioQueueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const isPlayingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const audioBufferRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)('');\n    const bufferTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { startStreaming, stopStreaming } = (0,voice_stream__WEBPACK_IMPORTED_MODULE_1__.useVoiceStream)({\n        onAudioChunked: {\n            \"useAgentConversation.useVoiceStream\": (audioData)=>{\n                if (!websocketRef.current) return;\n                sendMessage(websocketRef.current, {\n                    user_audio_chunk: audioData\n                });\n            }\n        }[\"useAgentConversation.useVoiceStream\"]\n    });\n    // Initialize audio context\n    const initAudioContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[initAudioContext]\": async ()=>{\n            if (!audioContextRef.current) {\n                try {\n                    const AudioContextClass = window.AudioContext || window.webkitAudioContext;\n                    audioContextRef.current = new AudioContextClass();\n                    // Resume audio context if it's suspended (required by some browsers)\n                    if (audioContextRef.current.state === 'suspended') {\n                        await audioContextRef.current.resume();\n                    }\n                } catch (error) {\n                    console.error('Failed to initialize audio context:', error);\n                }\n            }\n        }\n    }[\"useAgentConversation.useCallback[initAudioContext]\"], []);\n    // Play audio from base64 using HTML Audio element\n    const playAudio = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[playAudio]\": async (base64Audio)=>{\n            try {\n                console.log('Attempting to play audio chunk, length:', base64Audio.length);\n                // Try different audio formats that ElevenLabs might use\n                const audioFormats = [\n                    'audio/mpeg',\n                    'audio/mp3',\n                    'audio/wav',\n                    'audio/ogg',\n                    'audio/webm'\n                ];\n                for (const format of audioFormats){\n                    try {\n                        const audio = new Audio(\"data:\".concat(format, \";base64,\").concat(base64Audio));\n                        audio.volume = 0.8;\n                        const playPromise = new Promise({\n                            \"useAgentConversation.useCallback[playAudio]\": (resolve, reject)=>{\n                                let resolved = false;\n                                const cleanup = {\n                                    \"useAgentConversation.useCallback[playAudio].cleanup\": ()=>{\n                                        audio.onended = null;\n                                        audio.onerror = null;\n                                        audio.oncanplaythrough = null;\n                                        audio.onloadeddata = null;\n                                    }\n                                }[\"useAgentConversation.useCallback[playAudio].cleanup\"];\n                                audio.onended = ({\n                                    \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                        if (!resolved) {\n                                            resolved = true;\n                                            cleanup();\n                                            console.log(\"Audio played successfully with format: \".concat(format));\n                                            resolve();\n                                        }\n                                    }\n                                })[\"useAgentConversation.useCallback[playAudio]\"];\n                                audio.onerror = ({\n                                    \"useAgentConversation.useCallback[playAudio]\": (error)=>{\n                                        if (!resolved) {\n                                            cleanup();\n                                            console.log(\"Failed to play with format \".concat(format, \":\"), error);\n                                            reject(error);\n                                        }\n                                    }\n                                })[\"useAgentConversation.useCallback[playAudio]\"];\n                                audio.oncanplaythrough = ({\n                                    \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                        console.log(\"Audio ready to play with format: \".concat(format));\n                                        audio.play().catch(reject);\n                                    }\n                                })[\"useAgentConversation.useCallback[playAudio]\"];\n                                // Fallback timeout\n                                setTimeout({\n                                    \"useAgentConversation.useCallback[playAudio]\": ()=>{\n                                        if (!resolved && audio.readyState >= 2) {\n                                            audio.play().catch(reject);\n                                        }\n                                    }\n                                }[\"useAgentConversation.useCallback[playAudio]\"], 200);\n                            }\n                        }[\"useAgentConversation.useCallback[playAudio]\"]);\n                        await playPromise;\n                        return; // Success, exit the loop\n                    } catch (formatError) {\n                        console.log(\"Format \".concat(format, \" failed, trying next...\"));\n                        continue;\n                    }\n                }\n                throw new Error('All audio formats failed');\n            } catch (error) {\n                console.error('Error playing audio with all formats:', error);\n            // Don't throw, just log and continue\n            }\n        }\n    }[\"useAgentConversation.useCallback[playAudio]\"], []);\n    // Process audio queue\n    const processAudioQueue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[processAudioQueue]\": async ()=>{\n            if (isPlayingRef.current || audioQueueRef.current.length === 0) return;\n            isPlayingRef.current = true;\n            const audioData = audioQueueRef.current.shift();\n            if (audioData) {\n                try {\n                    await playAudio(audioData);\n                } catch (error) {\n                    console.error('Error in processAudioQueue:', error);\n                // Continue processing even if one audio chunk fails\n                }\n            }\n            isPlayingRef.current = false;\n            // Process next audio in queue\n            if (audioQueueRef.current.length > 0) {\n                setTimeout({\n                    \"useAgentConversation.useCallback[processAudioQueue]\": ()=>processAudioQueue()\n                }[\"useAgentConversation.useCallback[processAudioQueue]\"], 50); // Small delay between chunks\n            }\n        }\n    }[\"useAgentConversation.useCallback[processAudioQueue]\"], [\n        playAudio\n    ]);\n    // Buffer audio chunks and play when ready\n    const bufferAudioChunk = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[bufferAudioChunk]\": (base64Audio)=>{\n            // Add chunk to buffer\n            audioBufferRef.current += base64Audio;\n            // Clear existing timeout\n            if (bufferTimeoutRef.current) {\n                clearTimeout(bufferTimeoutRef.current);\n            }\n            // Set timeout to play buffered audio\n            bufferTimeoutRef.current = setTimeout({\n                \"useAgentConversation.useCallback[bufferAudioChunk]\": ()=>{\n                    if (audioBufferRef.current.length > 0) {\n                        console.log('Playing buffered audio, total length:', audioBufferRef.current.length);\n                        audioQueueRef.current.push(audioBufferRef.current);\n                        audioBufferRef.current = ''; // Clear buffer\n                        processAudioQueue();\n                    }\n                }\n            }[\"useAgentConversation.useCallback[bufferAudioChunk]\"], 500); // Wait 500ms for more chunks\n        }\n    }[\"useAgentConversation.useCallback[bufferAudioChunk]\"], [\n        processAudioQueue\n    ]);\n    const startConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n            if (isConnected) return;\n            try {\n                // Request microphone permission\n                await navigator.mediaDevices.getUserMedia({\n                    audio: true\n                });\n                // Get signed URL for WebSocket connection\n                const response = await fetch(\"/api/conversation/signed-url?agentId=\".concat(agentId));\n                if (!response.ok) {\n                    throw new Error('Failed to get signed URL');\n                }\n                const { signed_url } = await response.json();\n                const websocket = new WebSocket(signed_url);\n                websocket.onopen = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket connected');\n                        setIsConnected(true);\n                        sendMessage(websocket, {\n                            type: \"conversation_initiation_client_data\"\n                        });\n                        await startStreaming();\n                        setIsListening(true);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onmessage = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async (event)=>{\n                        const data = JSON.parse(event.data);\n                        // Handle ping events to keep connection alive\n                        if (data.type === \"ping\") {\n                            setTimeout({\n                                \"useAgentConversation.useCallback[startConversation]\": ()=>{\n                                    sendMessage(websocket, {\n                                        type: \"pong\",\n                                        event_id: data.ping_event.event_id\n                                    });\n                                }\n                            }[\"useAgentConversation.useCallback[startConversation]\"], data.ping_event.ping_ms || 0);\n                        }\n                        if (data.type === \"user_transcript\") {\n                            const { user_transcription_event } = data;\n                            setUserTranscript(user_transcription_event.user_transcript);\n                            console.log(\"User transcript:\", user_transcription_event.user_transcript);\n                        }\n                        if (data.type === \"agent_response\") {\n                            const { agent_response_event } = data;\n                            setAgentResponse(agent_response_event.agent_response);\n                            console.log(\"Agent response:\", agent_response_event.agent_response);\n                        }\n                        if (data.type === \"interruption\") {\n                            console.log(\"Conversation interrupted\");\n                            // Clear audio queue on interruption\n                            audioQueueRef.current = [];\n                            isPlayingRef.current = false;\n                        }\n                        if (data.type === \"audio\") {\n                            const { audio_event } = data;\n                            console.log('Received audio chunk, length:', audio_event.audio_base_64.length);\n                            // Add audio to queue for sequential playback\n                            audioQueueRef.current.push(audio_event.audio_base_64);\n                            processAudioQueue();\n                        }\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocketRef.current = websocket;\n                websocket.onclose = ({\n                    \"useAgentConversation.useCallback[startConversation]\": async ()=>{\n                        console.log('WebSocket disconnected');\n                        websocketRef.current = null;\n                        setIsConnected(false);\n                        setIsListening(false);\n                        stopStreaming();\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n                websocket.onerror = ({\n                    \"useAgentConversation.useCallback[startConversation]\": (error)=>{\n                        console.error('WebSocket error:', error);\n                    }\n                })[\"useAgentConversation.useCallback[startConversation]\"];\n            } catch (error) {\n                console.error('Failed to start conversation:', error);\n                throw error;\n            }\n        }\n    }[\"useAgentConversation.useCallback[startConversation]\"], [\n        agentId,\n        isConnected,\n        startStreaming,\n        initAudioContext,\n        processAudioQueue\n    ]);\n    const stopConversation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useAgentConversation.useCallback[stopConversation]\": async ()=>{\n            if (!websocketRef.current) return;\n            websocketRef.current.close();\n            setUserTranscript('');\n            setAgentResponse('');\n            audioQueueRef.current = [];\n            isPlayingRef.current = false;\n        }\n    }[\"useAgentConversation.useCallback[stopConversation]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useAgentConversation.useEffect\": ()=>{\n            return ({\n                \"useAgentConversation.useEffect\": ()=>{\n                    if (websocketRef.current) {\n                        websocketRef.current.close();\n                    }\n                }\n            })[\"useAgentConversation.useEffect\"];\n        }\n    }[\"useAgentConversation.useEffect\"], []);\n    return {\n        startConversation,\n        stopConversation,\n        isConnected,\n        isListening,\n        userTranscript,\n        agentResponse\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAgentConversation.ts\n"));

/***/ })

});
/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const VoiceSharingResponseModelCategory: core.serialization.Schema<serializers.VoiceSharingResponseModelCategory.Raw, ElevenLabs.VoiceSharingResponseModelCategory>;
export declare namespace VoiceSharingResponseModelCategory {
    type Raw = "generated" | "cloned" | "premade" | "professional" | "famous" | "high_quality";
}

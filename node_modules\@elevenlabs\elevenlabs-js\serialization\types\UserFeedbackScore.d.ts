/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const UserFeedbackScore: core.serialization.Schema<serializers.UserFeedbackScore.Raw, ElevenLabs.UserFeedbackScore>;
export declare namespace UserFeedbackScore {
    type Raw = "like" | "dislike";
}
